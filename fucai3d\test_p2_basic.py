"""
P2高级特征工程系统基础测试
验证AdvancedFeatureEngineer核心类的基本功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.data.advanced_feature_engineer import AdvancedFeatureEngineer

def test_advanced_feature_engineer():
    """测试AdvancedFeatureEngineer基本功能"""
    print("=== P2高级特征工程系统基础测试 ===")
    
    try:
        # 初始化高级特征工程引擎
        db_path = "data/fucai3d.db"
        engineer = AdvancedFeatureEngineer(db_path, cache_enabled=True, cache_size=100)
        print("✅ AdvancedFeatureEngineer初始化成功")
        
        # 验证系统状态
        validation = engineer.validate_system()
        print(f"✅ 系统验证完成: {validation['overall_status']}")
        
        # 获取可用特征类型
        feature_types = engineer.get_available_feature_types()
        print(f"✅ 可用特征类型: {list(feature_types.keys())}")
        
        # 测试缓存统计
        cache_stats = engineer.get_cache_stats()
        print(f"✅ 缓存系统状态: 启用={cache_stats['cache_enabled']}, 大小={cache_stats['cache_size']}")
        
        # 测试特征获取（如果数据库存在）
        if validation['base_system']['status'] == 'success':
            latest_issue = validation['base_system']['latest_issue']
            if latest_issue:
                print(f"📊 测试最新期号 {latest_issue} 的特征获取...")
                
                # 测试获取百位特征
                hundreds_features = engineer.get_features_with_cache(latest_issue, 'hundreds')
                if hundreds_features:
                    print(f"✅ 百位特征获取成功，特征数量: {len(hundreds_features)}")
                
                # 测试缓存命中
                hundreds_features_cached = engineer.get_features_with_cache(latest_issue, 'hundreds')
                cache_stats_after = engineer.get_cache_stats()
                print(f"✅ 缓存测试完成，命中率: {cache_stats_after['hit_rate']:.2%}")
        
        print("\n🎉 P2高级特征工程系统基础测试全部通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_advanced_feature_engineer()
    sys.exit(0 if success else 1)
