# P9-闭环自动优化系统

## 项目概述
**前置条件**：P8-智能交集融合系统完成  
**核心目标**：实现全自动闭环优化机制  
**预计时间**：1-2周  

## 技术要求

### 闭环目标
- **自动数据更新**：定时采集最新开奖数据
- **自动模型重训练**：基于新数据更新模型
- **自动性能评估**：评估预测效果并调整策略
- **自动参数优化**：动态调整模型参数和权重
- **自动异常处理**：检测并处理系统异常

### 系统架构
- **数据监控器**：监控数据源变化
- **性能评估器**：评估各模型表现
- **优化决策器**：决定优化策略
- **模型更新器**：执行模型重训练
- **参数调优器**：自动调整参数
- **异常处理器**：处理系统异常

### 数据库扩展
```sql
-- 闭环优化日志表
CREATE TABLE optimization_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    optimization_type TEXT NOT NULL,    -- data_update/model_retrain/parameter_tune/performance_eval
    trigger_reason TEXT NOT NULL,       -- scheduled/performance_drop/data_change/manual
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    status TEXT NOT NULL,               -- running/completed/failed/cancelled
    details TEXT,                       -- JSON格式的详细信息
    performance_before TEXT,            -- 优化前性能(JSON)
    performance_after TEXT,             -- 优化后性能(JSON)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 系统性能监控表
CREATE TABLE system_performance_monitor (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    monitor_time TIMESTAMP NOT NULL,
    component_name TEXT NOT NULL,       -- hundreds/tens/units/sum/span/fusion
    performance_metric TEXT NOT NULL,   -- accuracy/mae/rmse/hit_rate
    current_value REAL NOT NULL,
    baseline_value REAL,
    threshold_value REAL,
    status TEXT NOT NULL,               -- normal/warning/critical
    alert_sent BOOLEAN DEFAULT FALSE,
    notes TEXT
);

-- 自动优化配置表
CREATE TABLE auto_optimization_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_name TEXT NOT NULL,
    config_type TEXT NOT NULL,          -- schedule/threshold/parameter
    config_value TEXT NOT NULL,         -- JSON格式配置值
    is_active BOOLEAN DEFAULT TRUE,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by TEXT DEFAULT 'system'
);

-- 模型版本管理表
CREATE TABLE model_versions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    model_name TEXT NOT NULL,
    version_number TEXT NOT NULL,
    model_path TEXT NOT NULL,
    performance_metrics TEXT,           -- JSON格式性能指标
    training_data_range TEXT,           -- 训练数据范围
    is_active BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT
);
```

## 核心功能实现

### 1. 闭环优化控制器
```python
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
import sqlite3
import json
import logging
import schedule
import time
import threading
from datetime import datetime, timedelta
import pickle
import os
from concurrent.futures import ThreadPoolExecutor

class ClosedLoopOptimizer:
    def __init__(self, db_path: str, config_path: str = "config/"):
        self.db_path = db_path
        self.config_path = config_path
        self.logger = logging.getLogger(__name__)
        
        # 组件引用
        self.data_collector = None
        self.model_trainers = {}
        self.fusion_predictor = None
        
        # 优化配置
        self.optimization_config = {
            'data_update_interval': 3600,      # 数据更新间隔(秒)
            'performance_check_interval': 1800, # 性能检查间隔(秒)
            'retrain_threshold': 0.1,          # 重训练阈值
            'parameter_tune_interval': 86400,   # 参数调优间隔(秒)
            'max_concurrent_tasks': 3,          # 最大并发任务数
            'backup_retention_days': 30         # 备份保留天数
        }
        
        # 性能阈值
        self.performance_thresholds = {
            'hundreds_accuracy': 0.30,
            'tens_accuracy': 0.30,
            'units_accuracy': 0.30,
            'sum_mae': 2.0,
            'span_mae': 1.5,
            'fusion_hit_rate': 0.15
        }
        
        # 运行状态
        self.is_running = False
        self.optimization_thread = None
        self.executor = ThreadPoolExecutor(max_workers=self.optimization_config['max_concurrent_tasks'])
        
        # 初始化
        self.load_optimization_config()
        self.setup_scheduler()
    
    def load_optimization_config(self):
        """加载优化配置"""
        conn = sqlite3.connect(self.db_path)
        
        query = """
            SELECT config_name, config_type, config_value
            FROM auto_optimization_config
            WHERE is_active = TRUE
        """
        
        df = pd.read_sql_query(query, conn)
        
        for _, row in df.iterrows():
            config_name = row['config_name']
            config_value = json.loads(row['config_value'])
            
            if config_name in self.optimization_config:
                self.optimization_config[config_name] = config_value
            elif config_name.endswith('_threshold'):
                threshold_name = config_name.replace('_threshold', '')
                if threshold_name in self.performance_thresholds:
                    self.performance_thresholds[threshold_name] = config_value
        
        conn.close()
        self.logger.info("优化配置加载完成")
    
    def setup_scheduler(self):
        """设置定时任务"""
        # 数据更新任务
        schedule.every(self.optimization_config['data_update_interval']).seconds.do(
            self.schedule_data_update
        )
        
        # 性能检查任务
        schedule.every(self.optimization_config['performance_check_interval']).seconds.do(
            self.schedule_performance_check
        )
        
        # 参数调优任务
        schedule.every(self.optimization_config['parameter_tune_interval']).seconds.do(
            self.schedule_parameter_tuning
        )
        
        # 每日维护任务
        schedule.every().day.at("02:00").do(self.schedule_daily_maintenance)
        
        self.logger.info("定时任务设置完成")
    
    def start_optimization_loop(self):
        """启动优化循环"""
        if self.is_running:
            self.logger.warning("优化循环已在运行")
            return
        
        self.is_running = True
        self.optimization_thread = threading.Thread(target=self._optimization_loop, daemon=True)
        self.optimization_thread.start()
        
        self.logger.info("闭环优化系统已启动")
    
    def stop_optimization_loop(self):
        """停止优化循环"""
        self.is_running = False
        if self.optimization_thread:
            self.optimization_thread.join(timeout=10)
        
        self.executor.shutdown(wait=True)
        self.logger.info("闭环优化系统已停止")
    
    def _optimization_loop(self):
        """优化循环主体"""
        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                self.logger.error(f"优化循环异常: {e}")
                time.sleep(300)  # 异常时等待5分钟
    
    def schedule_data_update(self):
        """调度数据更新任务"""
        if not self.is_running:
            return
        
        future = self.executor.submit(self.execute_data_update)
        future.add_done_callback(self._handle_task_completion)
    
    def schedule_performance_check(self):
        """调度性能检查任务"""
        if not self.is_running:
            return
        
        future = self.executor.submit(self.execute_performance_check)
        future.add_done_callback(self._handle_task_completion)
    
    def schedule_parameter_tuning(self):
        """调度参数调优任务"""
        if not self.is_running:
            return
        
        future = self.executor.submit(self.execute_parameter_tuning)
        future.add_done_callback(self._handle_task_completion)
    
    def schedule_daily_maintenance(self):
        """调度日常维护任务"""
        if not self.is_running:
            return
        
        future = self.executor.submit(self.execute_daily_maintenance)
        future.add_done_callback(self._handle_task_completion)
    
    def _handle_task_completion(self, future):
        """处理任务完成"""
        try:
            result = future.result()
            self.logger.info(f"任务完成: {result}")
        except Exception as e:
            self.logger.error(f"任务执行失败: {e}")
    
    def execute_data_update(self) -> Dict:
        """执行数据更新"""
        log_id = self.log_optimization_start('data_update', 'scheduled')
        
        try:
            # 检查是否有新数据
            if self.data_collector is None:
                from src.data.collector import LotteryDataCollector
                self.data_collector = LotteryDataCollector(self.db_path)
            
            # 采集最新数据
            new_records = self.data_collector.collect_latest_data()
            
            if new_records > 0:
                self.logger.info(f"采集到 {new_records} 条新数据")
                
                # 更新特征工程
                self.update_features()
                
                # 触发性能检查
                self.schedule_performance_check()
                
                result = {'status': 'success', 'new_records': new_records}
            else:
                result = {'status': 'no_new_data', 'new_records': 0}
            
            self.log_optimization_end(log_id, 'completed', result)
            return result
            
        except Exception as e:
            self.logger.error(f"数据更新失败: {e}")
            self.log_optimization_end(log_id, 'failed', {'error': str(e)})
            return {'status': 'failed', 'error': str(e)}
    
    def execute_performance_check(self) -> Dict:
        """执行性能检查"""
        log_id = self.log_optimization_start('performance_eval', 'scheduled')
        
        try:
            # 获取最近的性能数据
            performance_data = self.get_recent_performance()
            
            # 检查是否需要优化
            optimization_needed = self.analyze_performance_degradation(performance_data)
            
            if optimization_needed:
                self.logger.info("检测到性能下降，触发模型重训练")
                self.schedule_model_retraining(optimization_needed)
            
            # 更新性能监控
            self.update_performance_monitor(performance_data)
            
            result = {
                'status': 'success',
                'performance_data': performance_data,
                'optimization_needed': optimization_needed
            }
            
            self.log_optimization_end(log_id, 'completed', result)
            return result
            
        except Exception as e:
            self.logger.error(f"性能检查失败: {e}")
            self.log_optimization_end(log_id, 'failed', {'error': str(e)})
            return {'status': 'failed', 'error': str(e)}
    
    def get_recent_performance(self) -> Dict:
        """获取最近的性能数据"""
        conn = sqlite3.connect(self.db_path)
        
        # 获取最近30天的性能数据
        query = """
            SELECT 
                AVG(CASE WHEN hundreds_accuracy THEN 1.0 ELSE 0.0 END) as hundreds_accuracy,
                AVG(CASE WHEN tens_accuracy THEN 1.0 ELSE 0.0 END) as tens_accuracy,
                AVG(CASE WHEN units_accuracy THEN 1.0 ELSE 0.0 END) as units_accuracy,
                AVG(CASE WHEN hit_type = 'exact' THEN 1.0 ELSE 0.0 END) as fusion_hit_rate,
                COUNT(*) as total_predictions
            FROM prediction_performance
            WHERE evaluated_at >= datetime('now', '-30 days')
        """
        
        performance = pd.read_sql_query(query, conn).iloc[0].to_dict()
        
        # 获取和值跨度性能
        sum_span_query = """
            SELECT 
                AVG(ABS(predicted_sum - actual_sum)) as sum_mae,
                AVG(ABS(predicted_span - actual_span)) as span_mae
            FROM (
                SELECT sp.predicted_sum, pp.actual_sum, 
                       spa.predicted_span, pp.actual_span
                FROM prediction_performance pp
                LEFT JOIN sum_predictions sp ON pp.issue = sp.issue AND sp.model_type = 'ensemble'
                LEFT JOIN span_predictions spa ON pp.issue = spa.issue AND spa.model_type = 'ensemble'
                WHERE pp.evaluated_at >= datetime('now', '-30 days')
            )
        """
        
        sum_span_perf = pd.read_sql_query(sum_span_query, conn).iloc[0].to_dict()
        performance.update(sum_span_perf)
        
        conn.close()
        return performance
    
    def analyze_performance_degradation(self, performance_data: Dict) -> List[str]:
        """分析性能下降"""
        degraded_components = []
        
        for metric, current_value in performance_data.items():
            if metric in self.performance_thresholds and current_value is not None:
                threshold = self.performance_thresholds[metric]
                
                # 对于误差指标，值越小越好
                if metric.endswith('_mae'):
                    if current_value > threshold:
                        degraded_components.append(metric.replace('_mae', ''))
                # 对于准确率指标，值越大越好
                else:
                    if current_value < threshold:
                        degraded_components.append(metric.replace('_accuracy', '').replace('_hit_rate', ''))
        
        return degraded_components
    
    def schedule_model_retraining(self, components: List[str]):
        """调度模型重训练"""
        for component in components:
            future = self.executor.submit(self.retrain_component_model, component)
            future.add_done_callback(self._handle_task_completion)
    
    def retrain_component_model(self, component: str) -> Dict:
        """重训练组件模型"""
        log_id = self.log_optimization_start('model_retrain', f'performance_drop_{component}')
        
        try:
            self.logger.info(f"开始重训练 {component} 模型")
            
            # 备份当前模型
            self.backup_current_model(component)
            
            # 获取训练器
            trainer = self.get_model_trainer(component)
            
            if trainer is None:
                raise ValueError(f"未找到 {component} 的训练器")
            
            # 执行重训练
            performance_before = self.get_component_performance(component)
            
            training_result = trainer.retrain_with_latest_data()
            
            performance_after = self.get_component_performance(component)
            
            # 评估重训练效果
            improvement = self.evaluate_training_improvement(
                performance_before, performance_after, component
            )
            
            if improvement['is_better']:
                self.logger.info(f"{component} 模型重训练成功，性能提升: {improvement['improvement']:.3f}")
                self.save_model_version(component, training_result)
            else:
                self.logger.warning(f"{component} 模型重训练后性能未提升，回滚到原模型")
                self.rollback_model(component)
            
            result = {
                'status': 'success',
                'component': component,
                'performance_before': performance_before,
                'performance_after': performance_after,
                'improvement': improvement
            }
            
            self.log_optimization_end(log_id, 'completed', result)
            return result
            
        except Exception as e:
            self.logger.error(f"{component} 模型重训练失败: {e}")
            self.rollback_model(component)
            self.log_optimization_end(log_id, 'failed', {'error': str(e)})
            return {'status': 'failed', 'component': component, 'error': str(e)}
    
    def execute_parameter_tuning(self) -> Dict:
        """执行参数调优"""
        log_id = self.log_optimization_start('parameter_tune', 'scheduled')
        
        try:
            tuning_results = {}
            
            # 调优融合权重
            fusion_result = self.tune_fusion_weights()
            tuning_results['fusion'] = fusion_result
            
            # 调优模型超参数
            for component in ['hundreds', 'tens', 'units', 'sum', 'span']:
                component_result = self.tune_component_parameters(component)
                tuning_results[component] = component_result
            
            result = {
                'status': 'success',
                'tuning_results': tuning_results
            }
            
            self.log_optimization_end(log_id, 'completed', result)
            return result
            
        except Exception as e:
            self.logger.error(f"参数调优失败: {e}")
            self.log_optimization_end(log_id, 'failed', {'error': str(e)})
            return {'status': 'failed', 'error': str(e)}
    
    def tune_fusion_weights(self) -> Dict:
        """调优融合权重"""
        if self.fusion_predictor is None:
            from src.prediction.fusion import FusionPredictor
            self.fusion_predictor = FusionPredictor(self.db_path)
        
        # 基于最近的表现调整权重
        self.fusion_predictor.fusion_engine.update_fusion_weights()
        
        return {'status': 'completed', 'method': 'performance_based'}
    
    def tune_component_parameters(self, component: str) -> Dict:
        """调优组件参数"""
        try:
            trainer = self.get_model_trainer(component)
            if trainer is None:
                return {'status': 'skipped', 'reason': 'no_trainer'}
            
            # 执行超参数优化
            optimization_result = trainer.optimize_hyperparameters()
            
            return {
                'status': 'completed',
                'best_params': optimization_result.get('best_params'),
                'best_score': optimization_result.get('best_score')
            }
            
        except Exception as e:
            self.logger.error(f"{component} 参数调优失败: {e}")
            return {'status': 'failed', 'error': str(e)}
    
    def execute_daily_maintenance(self) -> Dict:
        """执行日常维护"""
        log_id = self.log_optimization_start('daily_maintenance', 'scheduled')
        
        try:
            maintenance_tasks = []
            
            # 清理过期日志
            cleaned_logs = self.cleanup_old_logs()
            maintenance_tasks.append(f"清理日志: {cleaned_logs} 条")
            
            # 清理过期备份
            cleaned_backups = self.cleanup_old_backups()
            maintenance_tasks.append(f"清理备份: {cleaned_backups} 个")
            
            # 数据库优化
            self.optimize_database()
            maintenance_tasks.append("数据库优化完成")
            
            # 性能报告生成
            self.generate_performance_report()
            maintenance_tasks.append("性能报告生成完成")
            
            result = {
                'status': 'success',
                'maintenance_tasks': maintenance_tasks
            }
            
            self.log_optimization_end(log_id, 'completed', result)
            return result
            
        except Exception as e:
            self.logger.error(f"日常维护失败: {e}")
            self.log_optimization_end(log_id, 'failed', {'error': str(e)})
            return {'status': 'failed', 'error': str(e)}
    
    def get_model_trainer(self, component: str):
        """获取模型训练器"""
        if component not in self.model_trainers:
            try:
                if component == 'hundreds':
                    from src.models.hundreds_predictor import EnsembleHundredsPredictor
                    self.model_trainers[component] = EnsembleHundredsPredictor(self.db_path)
                elif component == 'tens':
                    from src.models.tens_predictor import EnsembleTensPredictor
                    self.model_trainers[component] = EnsembleTensPredictor(self.db_path)
                elif component == 'units':
                    from src.models.units_predictor import EnsembleUnitsPredictor
                    self.model_trainers[component] = EnsembleUnitsPredictor(self.db_path)
                elif component == 'sum':
                    from src.models.sum_predictor import EnsembleSumPredictor
                    self.model_trainers[component] = EnsembleSumPredictor(self.db_path)
                elif component == 'span':
                    from src.models.span_predictor import EnsembleSpanPredictor
                    self.model_trainers[component] = EnsembleSpanPredictor(self.db_path)
            except ImportError as e:
                self.logger.error(f"无法导入 {component} 训练器: {e}")
                return None
        
        return self.model_trainers.get(component)
    
    def backup_current_model(self, component: str):
        """备份当前模型"""
        backup_dir = os.path.join(self.config_path, 'model_backups', component)
        os.makedirs(backup_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = os.path.join(backup_dir, f'model_{timestamp}.pkl')
        
        # 这里应该保存实际的模型文件
        # 具体实现取决于模型的保存格式
        
        self.logger.info(f"{component} 模型已备份到: {backup_path}")
    
    def log_optimization_start(self, optimization_type: str, trigger_reason: str) -> int:
        """记录优化开始"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO optimization_logs 
            (optimization_type, trigger_reason, start_time, status)
            VALUES (?, ?, ?, ?)
        """, (optimization_type, trigger_reason, datetime.now(), 'running'))
        
        log_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return log_id
    
    def log_optimization_end(self, log_id: int, status: str, details: Dict):
        """记录优化结束"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            UPDATE optimization_logs 
            SET end_time = ?, status = ?, details = ?
            WHERE id = ?
        """, (datetime.now(), status, json.dumps(details), log_id))
        
        conn.commit()
        conn.close()
    
    def update_performance_monitor(self, performance_data: Dict):
        """更新性能监控"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        monitor_time = datetime.now()
        
        for metric, value in performance_data.items():
            if value is not None:
                # 确定状态
                if metric in self.performance_thresholds:
                    threshold = self.performance_thresholds[metric]
                    
                    if metric.endswith('_mae'):
                        status = 'normal' if value <= threshold else 'warning'
                    else:
                        status = 'normal' if value >= threshold else 'warning'
                else:
                    status = 'normal'
                
                cursor.execute("""
                    INSERT INTO system_performance_monitor 
                    (monitor_time, component_name, performance_metric, current_value, 
                     threshold_value, status)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (monitor_time, metric.split('_')[0], metric, value, 
                      self.performance_thresholds.get(metric), status))
        
        conn.commit()
        conn.close()
    
    def cleanup_old_logs(self) -> int:
        """清理过期日志"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cutoff_date = datetime.now() - timedelta(days=self.optimization_config['backup_retention_days'])
        
        cursor.execute("""
            DELETE FROM optimization_logs 
            WHERE created_at < ?
        """, (cutoff_date,))
        
        deleted_count = cursor.rowcount
        conn.commit()
        conn.close()
        
        return deleted_count
    
    def cleanup_old_backups(self) -> int:
        """清理过期备份"""
        backup_base_dir = os.path.join(self.config_path, 'model_backups')
        if not os.path.exists(backup_base_dir):
            return 0
        
        cutoff_date = datetime.now() - timedelta(days=self.optimization_config['backup_retention_days'])
        deleted_count = 0
        
        for component_dir in os.listdir(backup_base_dir):
            component_path = os.path.join(backup_base_dir, component_dir)
            if os.path.isdir(component_path):
                for backup_file in os.listdir(component_path):
                    backup_path = os.path.join(component_path, backup_file)
                    if os.path.isfile(backup_path):
                        file_time = datetime.fromtimestamp(os.path.getctime(backup_path))
                        if file_time < cutoff_date:
                            os.remove(backup_path)
                            deleted_count += 1
        
        return deleted_count
    
    def optimize_database(self):
        """优化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 执行VACUUM清理数据库
        cursor.execute("VACUUM")
        
        # 重建索引
        cursor.execute("REINDEX")
        
        conn.commit()
        conn.close()
        
        self.logger.info("数据库优化完成")
    
    def generate_performance_report(self):
        """生成性能报告"""
        conn = sqlite3.connect(self.db_path)
        
        # 生成最近30天的性能报告
        report_data = {
            'report_date': datetime.now().isoformat(),
            'period': '30_days',
            'performance_summary': self.get_recent_performance(),
            'optimization_summary': self.get_optimization_summary(),
            'component_performance': self.get_component_performance_summary()
        }
        
        # 保存报告
        report_path = os.path.join(self.config_path, 'reports', 
                                 f"performance_report_{datetime.now().strftime('%Y%m%d')}.json")
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        conn.close()
        self.logger.info(f"性能报告已生成: {report_path}")
    
    def get_optimization_summary(self) -> Dict:
        """获取优化摘要"""
        conn = sqlite3.connect(self.db_path)
        
        query = """
            SELECT 
                optimization_type,
                COUNT(*) as total_count,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as success_count,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_count
            FROM optimization_logs
            WHERE created_at >= datetime('now', '-30 days')
            GROUP BY optimization_type
        """
        
        df = pd.read_sql_query(query, conn)
        summary = df.to_dict('records')
        
        conn.close()
        return summary
    
    def get_component_performance_summary(self) -> Dict:
        """获取组件性能摘要"""
        performance_data = self.get_recent_performance()
        
        summary = {}
        for metric, value in performance_data.items():
            if value is not None:
                component = metric.split('_')[0]
                if component not in summary:
                    summary[component] = {}
                summary[component][metric] = {
                    'current_value': value,
                    'threshold': self.performance_thresholds.get(metric),
                    'status': 'normal' if metric not in self.performance_thresholds or 
                             (value >= self.performance_thresholds[metric] if not metric.endswith('_mae') 
                              else value <= self.performance_thresholds[metric]) else 'warning'
                }
        
        return summary
```

### 2. 闭环优化管理器
```python
class ClosedLoopManager:
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.optimizer = ClosedLoopOptimizer(db_path)
        self.logger = logging.getLogger(__name__)
    
    def start_system(self):
        """启动闭环系统"""
        try:
            self.optimizer.start_optimization_loop()
            self.logger.info("闭环优化系统启动成功")
            return {'status': 'success', 'message': '闭环优化系统已启动'}
        except Exception as e:
            self.logger.error(f"闭环系统启动失败: {e}")
            return {'status': 'failed', 'error': str(e)}
    
    def stop_system(self):
        """停止闭环系统"""
        try:
            self.optimizer.stop_optimization_loop()
            self.logger.info("闭环优化系统停止成功")
            return {'status': 'success', 'message': '闭环优化系统已停止'}
        except Exception as e:
            self.logger.error(f"闭环系统停止失败: {e}")
            return {'status': 'failed', 'error': str(e)}
    
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        return {
            'is_running': self.optimizer.is_running,
            'optimization_config': self.optimizer.optimization_config,
            'performance_thresholds': self.optimizer.performance_thresholds,
            'recent_performance': self.optimizer.get_recent_performance(),
            'last_optimization': self.get_last_optimization_info()
        }
    
    def get_last_optimization_info(self) -> Dict:
        """获取最后一次优化信息"""
        conn = sqlite3.connect(self.db_path)
        
        query = """
            SELECT optimization_type, trigger_reason, start_time, end_time, status, details
            FROM optimization_logs
            ORDER BY start_time DESC
            LIMIT 1
        """
        
        df = pd.read_sql_query(query, conn)
        
        if not df.empty:
            result = df.iloc[0].to_dict()
            if result['details']:
                result['details'] = json.loads(result['details'])
        else:
            result = {}
        
        conn.close()
        return result
    
    def manual_trigger_optimization(self, optimization_type: str, component: str = None) -> Dict:
        """手动触发优化"""
        try:
            if optimization_type == 'data_update':
                result = self.optimizer.execute_data_update()
            elif optimization_type == 'performance_check':
                result = self.optimizer.execute_performance_check()
            elif optimization_type == 'model_retrain' and component:
                result = self.optimizer.retrain_component_model(component)
            elif optimization_type == 'parameter_tune':
                result = self.optimizer.execute_parameter_tuning()
            elif optimization_type == 'daily_maintenance':
                result = self.optimizer.execute_daily_maintenance()
            else:
                return {'status': 'failed', 'error': '无效的优化类型或缺少参数'}
            
            return result
            
        except Exception as e:
            self.logger.error(f"手动优化失败: {e}")
            return {'status': 'failed', 'error': str(e)}
```

## 成功标准

### 自动化程度
- [ ] 数据自动更新成功率 > 95%
- [ ] 性能自动监控正常运行
- [ ] 模型自动重训练机制有效
- [ ] 参数自动调优功能正常

### 优化效果
- [ ] 系统性能持续改进
- [ ] 异常自动检测和处理
- [ ] 资源使用优化
- [ ] 预测准确率稳定提升

### 系统稳定性
- [ ] 闭环系统7×24小时稳定运行
- [ ] 异常恢复机制有效
- [ ] 日志记录完整
- [ ] 备份恢复机制可靠

## 部署说明

```python
# 使用示例
manager = ClosedLoopManager("data/lottery.db")

# 启动闭环系统
start_result = manager.start_system()
print(f"启动结果: {start_result}")

# 获取系统状态
status = manager.get_system_status()
print(f"系统状态: {status}")

# 手动触发优化
manual_result = manager.manual_trigger_optimization('performance_check')
print(f"手动优化结果: {manual_result}")

# 停止系统
stop_result = manager.stop_system()
print(f"停止结果: {stop_result}")
```

## 下一步
完成P9后，进入**P10-Web界面系统**开发阶段。
