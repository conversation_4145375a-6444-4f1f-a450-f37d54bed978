"""
福彩3D特征系统使用示例
展示如何使用新的特征计算系统，包括基础用法、高级功能和性能测试
"""

import sys
import os
import time
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.data.feature_calculator import LotteryFeatureCalculator, calculate_features
from src.data.feature_service import FeatureService
from src.database.models import LotteryData


def basic_usage_example():
    """基础使用示例"""
    print("=" * 60)
    print("1. 基础特征计算示例")
    print("=" * 60)
    
    # 创建特征计算器
    calculator = LotteryFeatureCalculator()
    
    # 计算开奖号码 123 的特征
    print("计算开奖号码 123 的特征:")
    result = calculator.calculate_all_features(1, 2, 3)
    
    # 显示基础特征
    print("\n基础特征:")
    for key, value in result.basic_features.items():
        print(f"  {key}: {value}")
    
    # 显示模式特征
    print("\n模式特征:")
    for key, value in result.pattern_features.items():
        print(f"  {key}: {value}")
    
    # 显示走势特征（无上期数据）
    print("\n走势特征:")
    for key, value in result.trend_features.items():
        print(f"  {key}: {value}")


def trend_calculation_example():
    """走势计算示例"""
    print("\n" + "=" * 60)
    print("2. 走势特征计算示例")
    print("=" * 60)
    
    calculator = LotteryFeatureCalculator()
    
    # 计算带走势的特征：当前期 456，上期 123
    print("当前期: 456, 上期: 123")
    result = calculator.calculate_all_features(4, 5, 6, 1, 2, 3)
    
    print("\n走势特征:")
    for key, value in result.trend_features.items():
        print(f"  {key}: {value}")


def convenience_function_example():
    """便捷函数使用示例"""
    print("\n" + "=" * 60)
    print("3. 便捷函数使用示例")
    print("=" * 60)
    
    # 使用便捷函数计算特征
    print("使用 calculate_features() 便捷函数:")
    features = calculate_features(7, 8, 9, 4, 5, 6)
    
    # 显示部分重要特征
    important_features = [
        'sum_value', 'sum_tail', 'span', 'odd_even_pattern',
        'big_small_pattern', 'prime_composite_pattern', 'route_012_pattern',
        'has_consecutive', 'number_type', 'overall_trend'
    ]
    
    print("\n重要特征:")
    for feature in important_features:
        if feature in features:
            print(f"  {feature}: {features[feature]}")


def lottery_data_integration_example():
    """LotteryData模型集成示例"""
    print("\n" + "=" * 60)
    print("4. LotteryData模型集成示例")
    print("=" * 60)
    
    # 创建测试数据
    current_data = LotteryData(
        issue="2025001", draw_date="2025-01-01",
        hundreds=3, tens=6, units=9
    )
    
    previous_data = LotteryData(
        issue="2024365", draw_date="2024-12-31",
        hundreds=1, tens=4, units=7
    )
    
    print(f"当前期: {current_data.issue} - {current_data.hundreds}{current_data.tens}{current_data.units}")
    print(f"上期: {previous_data.issue} - {previous_data.hundreds}{previous_data.tens}{previous_data.units}")
    
    # 获取所有特征
    features = current_data.get_features(previous_data)
    
    print(f"\n特征总数: {len(features)}")
    print("\n部分特征展示:")
    display_features = [
        'sum_value', 'sum_tail', 'big_medium_small_pattern',
        'is_ascending', 'overall_trend'
    ]
    
    for feature in display_features:
        if feature in features:
            print(f"  {feature}: {features[feature]}")
    
    # 获取单个特征
    print(f"\n单个特征获取示例:")
    sum_tail = current_data.get_feature_by_name('sum_tail', previous_data)
    print(f"  和尾: {sum_tail}")


def feature_service_example():
    """特征服务使用示例（需要数据库）"""
    print("\n" + "=" * 60)
    print("5. 特征服务使用示例")
    print("=" * 60)
    
    # 检查数据库是否存在
    db_path = "data/lottery.db"
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        print("请先运行数据采集程序创建数据库")
        return
    
    try:
        # 创建特征服务
        service = FeatureService(db_path)
        
        # 验证数据库
        validation = service.validate_database()
        print(f"数据库验证: {validation}")
        
        if validation['status'] != 'success':
            return
        
        # 获取可用特征列表
        available_features = service.get_available_features()
        print(f"\n可用特征数量: {len(available_features)}")
        
        # 获取最新期号的特征
        latest_issue = validation['latest_issue']
        if latest_issue:
            print(f"\n获取最新期号 {latest_issue} 的特征:")
            features = service.get_features_for_issue(latest_issue)
            if features:
                print(f"  期号: {features['issue']}")
                print(f"  开奖号码: {features['hundreds']}{features['tens']}{features['units']}")
                print(f"  和值: {features['sum_value']}")
                print(f"  和尾: {features['sum_tail']}")
                print(f"  奇偶模式: {features['odd_even_pattern']}")
                print(f"  大小模式: {features['big_small_pattern']}")
        
        # 计算自定义号码的特征
        print(f"\n计算自定义号码 888 的特征:")
        custom_features = service.calculate_feature_for_numbers(8, 8, 8)
        print(f"  和值: {custom_features['sum_value']}")
        print(f"  和尾: {custom_features['sum_tail']}")
        print(f"  号码类型: {custom_features['number_type']}")
        print(f"  是否豹子: {custom_features['is_leopard']}")
        
    except Exception as e:
        print(f"特征服务示例执行出错: {e}")


def performance_test():
    """性能测试"""
    print("\n" + "=" * 60)
    print("6. 性能测试")
    print("=" * 60)
    
    calculator = LotteryFeatureCalculator()
    
    # 测试单次计算性能
    start_time = time.time()
    for _ in range(1000):
        calculator.calculate_all_features(1, 2, 3, 0, 1, 2)
    single_calc_time = time.time() - start_time
    
    print(f"1000次特征计算耗时: {single_calc_time:.4f}秒")
    print(f"平均单次计算耗时: {single_calc_time/1000*1000:.4f}毫秒")
    
    # 测试批量计算性能
    test_data = [
        (i % 10, (i+1) % 10, (i+2) % 10) for i in range(1000)
    ]
    
    start_time = time.time()
    results = []
    for hundreds, tens, units in test_data:
        result = calculator.calculate_all_features(hundreds, tens, units)
        results.append(result.to_dict())
    batch_calc_time = time.time() - start_time
    
    print(f"1000期数据批量计算耗时: {batch_calc_time:.4f}秒")
    print(f"平均每期计算耗时: {batch_calc_time/1000*1000:.4f}毫秒")
    
    # 估算8359期历史数据处理时间
    estimated_time = batch_calc_time * 8359 / 1000
    print(f"估算8359期历史数据处理时间: {estimated_time:.2f}秒")


def feature_analysis_example():
    """特征分析示例"""
    print("\n" + "=" * 60)
    print("7. 特征分析示例")
    print("=" * 60)
    
    calculator = LotteryFeatureCalculator()
    
    # 分析不同类型号码的特征
    test_cases = [
        ("豹子", 5, 5, 5),
        ("对子", 1, 1, 2),
        ("组六", 1, 2, 3),
        ("连号", 4, 5, 6),
        ("大号", 7, 8, 9),
        ("小号", 0, 1, 2),
        ("全奇", 1, 3, 5),
        ("全偶", 2, 4, 6),
    ]
    
    print("不同类型号码的特征分析:")
    print("-" * 80)
    print(f"{'类型':<8} {'号码':<8} {'和值':<6} {'和尾':<6} {'跨度':<6} {'奇偶':<8} {'大小':<8} {'形态':<10}")
    print("-" * 80)
    
    for name, h, t, u in test_cases:
        result = calculator.calculate_all_features(h, t, u)
        features = result.to_dict()
        
        # 确定形态
        shape = "平行" if features['is_parallel'] else \
                "凸起" if features['is_convex'] else \
                "凹下" if features['is_concave'] else \
                "上升" if features['is_ascending'] else \
                "下降" if features['is_descending'] else "其他"
        
        print(f"{name:<8} {h}{t}{u:<8} {features['sum_value']:<6} {features['sum_tail']:<6} "
              f"{features['span']:<6} {features['odd_even_pattern']:<8} "
              f"{features['big_small_pattern']:<8} {shape:<10}")


def main():
    """主函数"""
    print("福彩3D特征系统使用示例")
    print("本示例展示了新特征计算系统的各种用法")
    
    # 运行所有示例
    basic_usage_example()
    trend_calculation_example()
    convenience_function_example()
    lottery_data_integration_example()
    feature_service_example()
    performance_test()
    feature_analysis_example()
    
    print("\n" + "=" * 60)
    print("示例运行完成！")
    print("=" * 60)
    print("\n使用说明:")
    print("1. 基础计算：使用 LotteryFeatureCalculator 类")
    print("2. 便捷函数：使用 calculate_features() 函数")
    print("3. 模型集成：使用 LotteryData.get_features() 方法")
    print("4. 服务接口：使用 FeatureService 类（需要数据库）")
    print("5. 所有特征都基于现有数据实时计算，无需修改数据库")


if __name__ == "__main__":
    main()
