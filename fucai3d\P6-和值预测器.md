# P6-和值预测器

## 项目概述
**前置条件**：P5-个位预测器完成  
**核心目标**：预测三位数和值0-27  
**预计时间**：1周  

## 技术要求

### 预测目标
- **输入**：和值专用特征向量
- **输出**：0-27范围内的数值预测
- **约束**：预测值在[0,27]区间内
- **精度要求**：预测误差±1.5以内

### 模型架构
- **XGBoost回归器**：主力模型，处理非线性关系
- **LightGBM回归器**：辅助模型，快速训练
- **LSTM回归器**：时序模型，捕获时间依赖
- **分布预测模型**：预测和值概率分布
- **约束优化模型**：结合位置预测进行约束优化
- **集成融合**：多模型加权融合

### 数据库扩展
```sql
-- 和值预测结果表
CREATE TABLE sum_predictions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    issue TEXT NOT NULL,
    model_type TEXT NOT NULL,           -- xgb/lgb/lstm/distribution/constraint/ensemble
    predicted_sum REAL NOT NULL,       -- 预测和值
    confidence REAL NOT NULL,          -- 预测置信度
    prediction_range_min INTEGER,      -- 预测范围最小值
    prediction_range_max INTEGER,      -- 预测范围最大值
    distribution_entropy REAL,         -- 分布熵值
    constraint_score REAL,             -- 约束一致性分数
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 和值模型性能表
CREATE TABLE sum_model_performance (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    model_type TEXT NOT NULL,
    evaluation_period TEXT NOT NULL,
    mae REAL NOT NULL,                  -- 平均绝对误差
    rmse REAL NOT NULL,                 -- 均方根误差
    accuracy_1 REAL NOT NULL,           -- ±1准确率
    accuracy_2 REAL NOT NULL,           -- ±2准确率
    r2_score REAL NOT NULL,             -- R²分数
    distribution_accuracy REAL,         -- 分布预测准确率
    evaluated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 和值分布统计表
CREATE TABLE sum_distribution_stats (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    sum_value INTEGER NOT NULL,
    frequency INTEGER NOT NULL,
    probability REAL NOT NULL,
    avg_span REAL,                      -- 该和值的平均跨度
    common_patterns TEXT,               -- 常见组合模式(JSON)
    seasonal_frequency TEXT,            -- 季节性频次(JSON)
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 和值约束规则表
CREATE TABLE sum_constraint_rules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_name TEXT NOT NULL,
    rule_description TEXT NOT NULL,
    min_sum INTEGER,
    max_sum INTEGER,
    span_constraint TEXT,               -- 跨度约束条件
    pattern_constraint TEXT,            -- 模式约束条件
    weight REAL DEFAULT 1.0,            -- 规则权重
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 核心功能实现

### 1. 和值预测器基类
```python
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Optional
import sqlite3
import json
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import xgboost as xgb
import lightgbm as lgb
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from scipy import stats
from scipy.optimize import minimize

class BaseSumPredictor(ABC):
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.model = None
        self.feature_names = None
        self.is_trained = False
        self.sum_distribution = None
        self.constraint_rules = None
        
    @abstractmethod
    def build_model(self):
        """构建模型"""
        pass
    
    @abstractmethod
    def train(self, X: np.ndarray, y: np.ndarray):
        """训练模型"""
        pass
    
    @abstractmethod
    def predict_sum(self, X: np.ndarray) -> np.ndarray:
        """预测和值"""
        pass
    
    def load_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """加载和值训练数据"""
        conn = sqlite3.connect(self.db_path)
        
        feature_query = """
            SELECT fd.feature_vector, ld.sum_value
            FROM feature_data fd
            JOIN lottery_data ld ON fd.issue = ld.issue
            WHERE fd.feature_type = 'sum'
            ORDER BY fd.issue
        """
        
        cursor = conn.cursor()
        cursor.execute(feature_query)
        rows = cursor.fetchall()
        
        if not rows:
            raise ValueError("没有找到和值特征数据")
        
        X = []
        y = []
        
        for row in rows:
            feature_vector = json.loads(row[0])
            sum_target = row[1]
            
            X.append(feature_vector)
            y.append(sum_target)
        
        # 获取特征名称
        cursor.execute("""
            SELECT feature_names FROM feature_data 
            WHERE feature_type = 'sum' 
            LIMIT 1
        """)
        feature_names_row = cursor.fetchone()
        if feature_names_row:
            self.feature_names = json.loads(feature_names_row[0])
        
        conn.close()
        
        return np.array(X), np.array(y)
    
    def build_sum_distribution(self):
        """构建和值分布统计"""
        conn = sqlite3.connect(self.db_path)
        
        # 统计和值分布
        query = """
            SELECT sum_value, span, hundreds, tens, units, 
                   strftime('%m', draw_date) as month,
                   COUNT(*) as frequency
            FROM lottery_data
            GROUP BY sum_value, span, hundreds, tens, units, month
            ORDER BY sum_value
        """
        
        df = pd.read_sql_query(query, conn)
        
        # 计算每个和值的统计信息
        sum_stats = {}
        
        for sum_val in range(28):  # 0-27
            sum_data = df[df['sum_value'] == sum_val]
            
            if len(sum_data) > 0:
                frequency = sum_data['frequency'].sum()
                avg_span = sum_data['span'].mean()
                
                # 常见组合模式
                patterns = []
                for _, row in sum_data.iterrows():
                    pattern = f"{row['hundreds']}{row['tens']}{row['units']}"
                    patterns.append(pattern)
                
                # 季节性频次
                seasonal_freq = {}
                for month in range(1, 13):
                    month_data = sum_data[sum_data['month'] == str(month)]
                    seasonal_freq[str(month)] = month_data['frequency'].sum()
                
                sum_stats[sum_val] = {
                    'frequency': frequency,
                    'probability': frequency / df['frequency'].sum(),
                    'avg_span': avg_span,
                    'common_patterns': patterns[:10],  # 前10个常见模式
                    'seasonal_frequency': seasonal_freq
                }
            else:
                sum_stats[sum_val] = {
                    'frequency': 0,
                    'probability': 0,
                    'avg_span': 0,
                    'common_patterns': [],
                    'seasonal_frequency': {}
                }
        
        self.sum_distribution = sum_stats
        
        # 保存到数据库
        self.save_sum_distribution()
        
        conn.close()
        
        return sum_stats
    
    def save_sum_distribution(self):
        """保存和值分布到数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 清空旧数据
        cursor.execute("DELETE FROM sum_distribution_stats")
        
        for sum_val, stats in self.sum_distribution.items():
            cursor.execute("""
                INSERT INTO sum_distribution_stats 
                (sum_value, frequency, probability, avg_span, common_patterns, seasonal_frequency)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (sum_val, stats['frequency'], stats['probability'], 
                  stats['avg_span'], json.dumps(stats['common_patterns']), 
                  json.dumps(stats['seasonal_frequency'])))
        
        conn.commit()
        conn.close()
    
    def load_constraint_rules(self):
        """加载约束规则"""
        conn = sqlite3.connect(self.db_path)
        
        query = """
            SELECT rule_name, rule_description, min_sum, max_sum, 
                   span_constraint, pattern_constraint, weight
            FROM sum_constraint_rules
            WHERE is_active = TRUE
        """
        
        df = pd.read_sql_query(query, conn)
        self.constraint_rules = df.to_dict('records')
        
        conn.close()
        
        return self.constraint_rules
    
    def apply_constraints(self, predicted_sum: float, 
                         position_predictions: Optional[Dict] = None) -> float:
        """应用约束条件优化预测"""
        if self.constraint_rules is None:
            self.load_constraint_rules()
        
        # 基础约束：确保在[0,27]范围内
        constrained_sum = np.clip(predicted_sum, 0, 27)
        
        # 如果有位置预测，进行约束优化
        if position_predictions is not None:
            constrained_sum = self.optimize_with_position_constraints(
                constrained_sum, position_predictions
            )
        
        return constrained_sum
    
    def optimize_with_position_constraints(self, predicted_sum: float, 
                                         position_predictions: Dict) -> float:
        """基于位置预测的约束优化"""
        hundreds_prob = position_predictions.get('hundreds_prob')
        tens_prob = position_predictions.get('tens_prob')
        units_prob = position_predictions.get('units_prob')
        
        if (hundreds_prob is None or tens_prob is None or units_prob is None):
            return predicted_sum
        
        # 计算期望和值
        expected_sum = 0
        for h in range(10):
            for t in range(10):
                for u in range(10):
                    combination_sum = h + t + u
                    combination_prob = hundreds_prob[h] * tens_prob[t] * units_prob[u]
                    expected_sum += combination_sum * combination_prob
        
        # 加权融合预测和值与期望和值
        weight = 0.7  # 预测和值权重
        optimized_sum = weight * predicted_sum + (1 - weight) * expected_sum
        
        return np.clip(optimized_sum, 0, 27)
    
    def evaluate_model(self, X_test: np.ndarray, y_test: np.ndarray) -> Dict:
        """评估模型性能"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        y_pred = self.predict_sum(X_test)
        
        # 计算各种误差指标
        mae = mean_absolute_error(y_test, y_pred)
        rmse = np.sqrt(mean_squared_error(y_test, y_pred))
        r2 = r2_score(y_test, y_pred)
        
        # 计算不同精度的准确率
        accuracy_1 = np.mean(np.abs(y_test - y_pred) <= 1)
        accuracy_2 = np.mean(np.abs(y_test - y_pred) <= 2)
        
        return {
            'mae': mae,
            'rmse': rmse,
            'r2_score': r2,
            'accuracy_1': accuracy_1,
            'accuracy_2': accuracy_2
        }
```

### 2. XGBoost和值预测器
```python
class XGBSumPredictor(BaseSumPredictor):
    def __init__(self, db_path: str):
        super().__init__(db_path)
        self.model_params = {
            'objective': 'reg:squarederror',
            'max_depth': 6,
            'learning_rate': 0.1,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'reg_alpha': 0.1,
            'reg_lambda': 1.0,
            'n_estimators': 200,
            'random_state': 42,
            'n_jobs': -1
        }
    
    def build_model(self):
        """构建XGBoost回归模型"""
        self.model = xgb.XGBRegressor(**self.model_params)
        return self.model
    
    def train(self, X: np.ndarray, y: np.ndarray):
        """训练XGBoost模型"""
        if self.model is None:
            self.build_model()
        
        # 构建和值分布
        self.build_sum_distribution()
        
        # 分割训练集和验证集
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        # 训练模型
        self.model.fit(
            X_train, y_train,
            eval_set=[(X_val, y_val)],
            early_stopping_rounds=20,
            verbose=False
        )
        
        self.is_trained = True
        
        # 评估模型
        val_performance = self.evaluate_model(X_val, y_val)
        print(f"XGBoost和值预测器验证集性能: {val_performance}")
        
        return val_performance
    
    def predict_sum(self, X: np.ndarray) -> np.ndarray:
        """预测和值"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        predictions = self.model.predict(X)
        
        # 应用约束
        constrained_predictions = []
        for pred in predictions:
            constrained_pred = self.apply_constraints(pred)
            constrained_predictions.append(constrained_pred)
        
        return np.array(constrained_predictions)
    
    def predict_with_confidence(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """预测和值及置信度"""
        predictions = self.predict_sum(X)
        
        # 基于分布计算置信度
        confidences = []
        for pred in predictions:
            # 找到最接近的整数和值
            closest_sum = int(round(pred))
            closest_sum = np.clip(closest_sum, 0, 27)
            
            # 基于历史概率计算置信度
            if self.sum_distribution and closest_sum in self.sum_distribution:
                confidence = self.sum_distribution[closest_sum]['probability']
            else:
                confidence = 0.01  # 最小置信度
            
            confidences.append(confidence)
        
        return predictions, np.array(confidences)
```

### 3. 分布预测器
```python
class DistributionSumPredictor(BaseSumPredictor):
    def __init__(self, db_path: str):
        super().__init__(db_path)
        self.distribution_model = None
        
    def build_model(self):
        """构建分布预测模型"""
        # 使用多分类模型预测和值分布
        self.model = xgb.XGBClassifier(
            objective='multi:softprob',
            num_class=28,  # 0-27共28个类别
            max_depth=6,
            learning_rate=0.1,
            n_estimators=200,
            random_state=42
        )
        return self.model
    
    def train(self, X: np.ndarray, y: np.ndarray):
        """训练分布预测模型"""
        if self.model is None:
            self.build_model()
        
        self.build_sum_distribution()
        
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        self.model.fit(
            X_train, y_train,
            eval_set=[(X_val, y_val)],
            early_stopping_rounds=20,
            verbose=False
        )
        
        self.is_trained = True
        
        # 评估分布预测准确率
        y_prob = self.model.predict_proba(X_val)
        y_pred = np.argmax(y_prob, axis=1)
        distribution_accuracy = np.mean(y_val == y_pred)
        
        val_performance = self.evaluate_model(X_val, y_val)
        val_performance['distribution_accuracy'] = distribution_accuracy
        
        print(f"分布和值预测器验证集性能: {val_performance}")
        
        return val_performance
    
    def predict_sum(self, X: np.ndarray) -> np.ndarray:
        """基于分布预测和值"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        # 获取概率分布
        probabilities = self.model.predict_proba(X)
        
        # 计算期望和值
        sum_values = np.arange(28)  # 0-27
        expected_sums = np.dot(probabilities, sum_values)
        
        return expected_sums
    
    def predict_distribution(self, X: np.ndarray) -> np.ndarray:
        """预测和值概率分布"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        return self.model.predict_proba(X)
    
    def get_prediction_range(self, X: np.ndarray, confidence_level: float = 0.8) -> List[Tuple[int, int]]:
        """获取预测范围"""
        probabilities = self.predict_distribution(X)
        
        ranges = []
        for prob in probabilities:
            # 找到累积概率达到confidence_level的范围
            sorted_indices = np.argsort(prob)[::-1]
            cumulative_prob = 0
            selected_sums = []
            
            for idx in sorted_indices:
                cumulative_prob += prob[idx]
                selected_sums.append(idx)
                if cumulative_prob >= confidence_level:
                    break
            
            if selected_sums:
                min_sum = min(selected_sums)
                max_sum = max(selected_sums)
                ranges.append((min_sum, max_sum))
            else:
                ranges.append((0, 27))
        
        return ranges
```

### 4. 约束优化预测器
```python
class ConstraintSumPredictor(BaseSumPredictor):
    def __init__(self, db_path: str):
        super().__init__(db_path)
        self.base_predictor = XGBSumPredictor(db_path)
        self.distribution_predictor = DistributionSumPredictor(db_path)
        
    def build_model(self):
        """构建约束优化模型"""
        self.base_predictor.build_model()
        self.distribution_predictor.build_model()
        self.load_constraint_rules()
        return self.base_predictor.model
    
    def train(self, X: np.ndarray, y: np.ndarray):
        """训练约束优化模型"""
        # 训练基础预测器
        base_performance = self.base_predictor.train(X, y)
        
        # 训练分布预测器
        dist_performance = self.distribution_predictor.train(X, y)
        
        self.is_trained = True
        
        return {
            'base': base_performance,
            'distribution': dist_performance
        }
    
    def predict_sum(self, X: np.ndarray) -> np.ndarray:
        """约束优化预测"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        # 获取基础预测
        base_predictions = self.base_predictor.predict_sum(X)
        
        # 获取分布预测
        dist_predictions = self.distribution_predictor.predict_sum(X)
        
        # 获取分布信息
        distributions = self.distribution_predictor.predict_distribution(X)
        
        # 约束优化融合
        optimized_predictions = []
        
        for i in range(len(X)):
            base_pred = base_predictions[i]
            dist_pred = dist_predictions[i]
            distribution = distributions[i]
            
            # 多目标优化
            def objective(x):
                # 目标1：接近基础预测
                base_loss = (x - base_pred) ** 2
                
                # 目标2：接近分布预测
                dist_loss = (x - dist_pred) ** 2
                
                # 目标3：符合历史分布
                x_int = int(round(np.clip(x, 0, 27)))
                dist_loss_hist = -np.log(distribution[x_int] + 1e-8)
                
                # 加权组合
                total_loss = 0.4 * base_loss + 0.3 * dist_loss + 0.3 * dist_loss_hist
                return total_loss
            
            # 约束优化
            result = minimize(objective, x0=base_pred, bounds=[(0, 27)], method='L-BFGS-B')
            optimized_pred = result.x[0]
            
            optimized_predictions.append(optimized_pred)
        
        return np.array(optimized_predictions)
    
    def predict_with_constraints(self, X: np.ndarray, 
                               position_predictions: Optional[Dict] = None) -> Dict:
        """带约束的完整预测"""
        base_pred = self.predict_sum(X)[0]
        
        # 应用位置约束
        if position_predictions:
            constrained_pred = self.optimize_with_position_constraints(
                base_pred, position_predictions
            )
        else:
            constrained_pred = base_pred
        
        # 获取预测范围
        ranges = self.distribution_predictor.get_prediction_range(X)
        pred_range = ranges[0] if ranges else (0, 27)
        
        # 计算置信度
        distributions = self.distribution_predictor.predict_distribution(X)
        closest_sum = int(round(np.clip(constrained_pred, 0, 27)))
        confidence = distributions[0][closest_sum]
        
        # 计算分布熵
        entropy = -np.sum(distributions[0] * np.log(distributions[0] + 1e-8))
        
        # 计算约束一致性分数
        constraint_score = self.calculate_constraint_score(
            constrained_pred, position_predictions
        )
        
        return {
            'predicted_sum': constrained_pred,
            'confidence': confidence,
            'prediction_range': pred_range,
            'distribution_entropy': entropy,
            'constraint_score': constraint_score,
            'distribution': distributions[0].tolist()
        }
    
    def calculate_constraint_score(self, predicted_sum: float, 
                                 position_predictions: Optional[Dict] = None) -> float:
        """计算约束一致性分数"""
        if position_predictions is None:
            return 0.5
        
        hundreds_prob = position_predictions.get('hundreds_prob')
        tens_prob = position_predictions.get('tens_prob')
        units_prob = position_predictions.get('units_prob')
        
        if (hundreds_prob is None or tens_prob is None or units_prob is None):
            return 0.5
        
        # 计算与位置预测的一致性
        consistency_scores = []
        target_sum = int(round(predicted_sum))
        
        for h in range(10):
            for t in range(10):
                for u in range(10):
                    if h + t + u == target_sum:
                        combination_prob = hundreds_prob[h] * tens_prob[t] * units_prob[u]
                        consistency_scores.append(combination_prob)
        
        # 返回所有可能组合的概率和
        return sum(consistency_scores) if consistency_scores else 0.0
```

### 5. 集成和值预测器
```python
class EnsembleSumPredictor:
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.xgb_predictor = XGBSumPredictor(db_path)
        self.lgb_predictor = LGBSumPredictor(db_path)  # 类似XGB实现
        self.distribution_predictor = DistributionSumPredictor(db_path)
        self.constraint_predictor = ConstraintSumPredictor(db_path)
        
        # 初始权重
        self.weights = {
            'xgb': 0.3,
            'lgb': 0.3,
            'distribution': 0.2,
            'constraint': 0.2
        }
        
        self.is_trained = False
    
    def train_all_models(self):
        """训练所有模型"""
        X, y = self.xgb_predictor.load_data()
        
        print("训练XGBoost和值预测器...")
        xgb_performance = self.xgb_predictor.train(X, y)
        
        print("训练LightGBM和值预测器...")
        lgb_performance = self.lgb_predictor.train(X, y)
        
        print("训练分布和值预测器...")
        dist_performance = self.distribution_predictor.train(X, y)
        
        print("训练约束优化和值预测器...")
        constraint_performance = self.constraint_predictor.train(X, y)
        
        # 根据性能调整权重
        self.adjust_weights(xgb_performance, lgb_performance, 
                          dist_performance, constraint_performance)
        
        self.is_trained = True
        
        return {
            'xgb': xgb_performance,
            'lgb': lgb_performance,
            'distribution': dist_performance,
            'constraint': constraint_performance,
            'weights': self.weights
        }
    
    def adjust_weights(self, xgb_perf: Dict, lgb_perf: Dict, 
                      dist_perf: Dict, constraint_perf: Dict):
        """根据性能调整权重"""
        # 使用MAE作为主要指标（越小越好）
        performances = {
            'xgb': 1 / (xgb_perf.get('mae', 1) + 0.1),
            'lgb': 1 / (lgb_perf.get('mae', 1) + 0.1),
            'distribution': 1 / (dist_perf.get('mae', 1) + 0.1),
            'constraint': 1 / (constraint_perf['base'].get('mae', 1) + 0.1)
        }
        
        total_perf = sum(performances.values())
        
        if total_perf > 0:
            for model_name in self.weights:
                self.weights[model_name] = performances[model_name] / total_perf
        
        print(f"调整后的和值预测器权重: {self.weights}")
    
    def predict_sum(self, X: np.ndarray, 
                   position_predictions: Optional[Dict] = None) -> np.ndarray:
        """集成预测和值"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        # 获取各模型预测
        xgb_pred = self.xgb_predictor.predict_sum(X)
        lgb_pred = self.lgb_predictor.predict_sum(X)
        dist_pred = self.distribution_predictor.predict_sum(X)
        
        # 约束预测需要位置信息
        if position_predictions:
            constraint_pred = self.constraint_predictor.predict_with_constraints(
                X, position_predictions
            )['predicted_sum']
            constraint_pred = np.array([constraint_pred])
        else:
            constraint_pred = self.constraint_predictor.predict_sum(X)
        
        # 加权融合
        ensemble_pred = (
            self.weights['xgb'] * xgb_pred +
            self.weights['lgb'] * lgb_pred +
            self.weights['distribution'] * dist_pred +
            self.weights['constraint'] * constraint_pred
        )
        
        return ensemble_pred
    
    def predict_next_period(self, issue: str, 
                          position_predictions: Optional[Dict] = None) -> Dict:
        """预测下一期和值"""
        # 获取最新特征
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT feature_vector FROM feature_data 
            WHERE feature_type = 'sum' 
            ORDER BY issue DESC 
            LIMIT 1
        """)
        
        row = cursor.fetchone()
        if not row:
            raise ValueError("没有找到最新的和值特征数据")
        
        latest_features = np.array(json.loads(row[0])).reshape(1, -1)
        
        # 预测和值
        predicted_sum = self.predict_sum(latest_features, position_predictions)[0]
        
        # 获取详细预测信息
        detailed_prediction = self.constraint_predictor.predict_with_constraints(
            latest_features, position_predictions
        )
        
        # 保存预测结果
        self.save_prediction(issue, detailed_prediction)
        
        conn.close()
        
        return {
            'issue': issue,
            'predicted_sum': predicted_sum,
            'confidence': detailed_prediction['confidence'],
            'prediction_range': detailed_prediction['prediction_range'],
            'distribution_entropy': detailed_prediction['distribution_entropy'],
            'constraint_score': detailed_prediction['constraint_score']
        }
    
    def save_prediction(self, issue: str, prediction_details: Dict):
        """保存预测结果"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT OR REPLACE INTO sum_predictions 
            (issue, model_type, predicted_sum, confidence, 
             prediction_range_min, prediction_range_max, 
             distribution_entropy, constraint_score)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (issue, 'ensemble', prediction_details['predicted_sum'], 
              prediction_details['confidence'], 
              prediction_details['prediction_range'][0],
              prediction_details['prediction_range'][1],
              prediction_details['distribution_entropy'],
              prediction_details['constraint_score']))
        
        conn.commit()
        conn.close()
```

## 成功标准

### 模型性能
- [ ] XGBoost MAE < 1.5
- [ ] LightGBM MAE < 1.5
- [ ] 分布预测准确率 > 60%
- [ ] 约束优化MAE < 1.3
- [ ] 集成模型MAE < 1.2

### 预测精度
- [ ] ±1准确率 > 60%
- [ ] ±2准确率 > 85%
- [ ] R²分数 > 0.6
- [ ] 约束一致性分数 > 0.7

### 系统稳定性
- [ ] 预测值在合理范围内
- [ ] 约束优化收敛稳定
- [ ] 分布预测合理

## 部署说明

```python
# 使用示例
predictor = EnsembleSumPredictor("data/lottery.db")

# 训练所有模型
performance = predictor.train_all_models()
print(f"和值预测器训练完成: {performance}")

# 预测下一期（可选位置预测信息）
position_predictions = {
    'hundreds_prob': np.array([0.1, 0.2, 0.15, 0.1, 0.05, 0.1, 0.1, 0.05, 0.1, 0.05]),
    'tens_prob': np.array([0.05, 0.1, 0.2, 0.15, 0.1, 0.1, 0.1, 0.05, 0.1, 0.05]),
    'units_prob': np.array([0.08, 0.12, 0.18, 0.12, 0.08, 0.12, 0.12, 0.08, 0.05, 0.05])
}

prediction = predictor.predict_next_period("2024001", position_predictions)
print(f"和值预测结果: {prediction}")
```

## 下一步
完成P6后，进入**P7-跨度预测器**开发阶段。
