#!/usr/bin/env python3
"""
测试Serena MCP配置脚本
验证Serena是否能正确启动并连接到fucai3d项目
"""

import subprocess
import sys
import json
import os

def test_serena_installation():
    """测试Serena安装"""
    print("🔍 测试Serena安装...")
    
    try:
        # 测试uv命令
        result = subprocess.run(
            ["uv", "--version"], 
            capture_output=True, 
            text=True, 
            cwd="D:/github/serena"
        )
        if result.returncode == 0:
            print(f"✅ UV版本: {result.stdout.strip()}")
        else:
            print(f"❌ UV命令失败: {result.stderr}")
            return False
            
        # 测试serena命令
        result = subprocess.run(
            ["uv", "run", "serena", "--help"], 
            capture_output=True, 
            text=True, 
            cwd="D:/github/serena"
        )
        if result.returncode == 0:
            print("✅ Serena命令可用")
        else:
            print(f"❌ Serena命令失败: {result.stderr}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_mcp_server_start():
    """测试MCP服务器启动"""
    print("\n🔍 测试MCP服务器启动...")
    
    try:
        # 测试MCP服务器启动（快速退出）
        cmd = [
            "uv", "run", "serena", "start-mcp-server",
            "--context", "ide-assistant",
            "--project", "D:/github/fucai3d",
            "--help"
        ]
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd="D:/github/serena",
            timeout=30
        )
        
        if result.returncode == 0:
            print("✅ MCP服务器命令正确")
            return True
        else:
            print(f"❌ MCP服务器启动失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ MCP服务器启动超时（这可能是正常的）")
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def show_config():
    """显示配置信息"""
    print("\n📋 Serena MCP配置信息:")
    
    config = {
        "mcpServers": {
            "serena": {
                "command": "C:/Program Files/Python311/Scripts/uv.exe",
                "args": [
                    "run",
                    "--directory", 
                    "D:/github/serena",
                    "serena",
                    "start-mcp-server",
                    "--context",
                    "ide-assistant", 
                    "--project",
                    "D:/github/fucai3d"
                ]
            }
        }
    }
    
    print(json.dumps(config, indent=2, ensure_ascii=False))
    
    print("\n📍 配置文件位置建议:")
    print("- Claude Desktop: %APPDATA%\\Claude\\claude_desktop_config.json")
    print("- Augment: 查看Augment配置目录")
    print("- 其他MCP客户端: 查看相应文档")

def main():
    """主函数"""
    print("🚀 Serena MCP配置测试")
    print("=" * 50)
    
    # 检查目录
    if not os.path.exists("D:/github/serena"):
        print("❌ Serena目录不存在: D:/github/serena")
        return False
        
    if not os.path.exists("D:/github/fucai3d"):
        print("❌ fucai3d项目目录不存在: D:/github/fucai3d")
        return False
    
    print("✅ 项目目录检查通过")
    
    # 测试安装
    if not test_serena_installation():
        return False
    
    # 测试MCP服务器
    if not test_mcp_server_start():
        return False
    
    # 显示配置
    show_config()
    
    print("\n🎉 所有测试通过！")
    print("📝 下一步：将上述配置添加到您的MCP客户端配置文件中")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
