# P11-系统集成与部署

## 项目概述
**前置条件**：P10-Web界面系统完成  
**核心目标**：完整系统集成和部署  
**预计时间**：1周  

## 技术要求

### 集成目标
- **模块整合**：所有子系统无缝集成
- **配置统一**：统一的配置管理
- **部署自动化**：一键部署和启动
- **监控完善**：完整的系统监控
- **文档齐全**：部署和使用文档

### 系统架构
```
福彩3D预测系统
├── 数据层 (Data Layer)
│   ├── SQLite数据库
│   ├── 数据采集器
│   └── 特征工程
├── 模型层 (Model Layer)
│   ├── 百位预测器
│   ├── 十位预测器
│   ├── 个位预测器
│   ├── 和值预测器
│   └── 跨度预测器
├── 融合层 (Fusion Layer)
│   ├── 智能交集融合
│   └── 约束优化
├── 优化层 (Optimization Layer)
│   ├── 闭环优化系统
│   └── 性能监控
└── 应用层 (Application Layer)
    ├── Web界面
    ├── API接口
    └── 系统管理
```

## 核心功能实现

### 1. 主应用程序 (main.py)
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
福彩3D智能预测系统主程序
"""

import os
import sys
import logging
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.logger import setup_logger
from src.utils.config import ConfigManager
from src.utils.database import DatabaseManager
from src.data.collector import LotteryDataCollector
from src.data.feature_engine import FeatureEngineer
from src.models.hundreds_predictor import EnsembleHundredsPredictor
from src.models.tens_predictor import EnsembleTensPredictor
from src.models.units_predictor import EnsembleUnitsPredictor
from src.models.sum_predictor import EnsembleSumPredictor
from src.models.span_predictor import EnsembleSpanPredictor
from src.prediction.fusion import FusionPredictor
from src.optimization.closed_loop import ClosedLoopManager
from src.web.app import create_app

class LotteryPredictionSystem:
    """福彩3D预测系统主类"""
    
    def __init__(self, config_path: str = "config/settings.yaml"):
        self.config_manager = ConfigManager(config_path)
        self.config = self.config_manager.get_config()
        
        # 设置日志
        self.logger = setup_logger(
            name="LotterySystem",
            log_file=self.config['logging']['file'],
            level=self.config['logging']['level']
        )
        
        # 初始化组件
        self.db_manager = None
        self.data_collector = None
        self.feature_engineer = None
        self.predictors = {}
        self.fusion_predictor = None
        self.closed_loop_manager = None
        self.web_app = None
        
        self.logger.info("福彩3D预测系统初始化完成")
    
    def initialize_database(self):
        """初始化数据库"""
        self.logger.info("初始化数据库...")
        
        db_config = self.config['database']
        self.db_manager = DatabaseManager(
            db_path=db_config['path'],
            backup_enabled=db_config.get('backup_enabled', True)
        )
        
        # 创建所有必要的表
        self.db_manager.initialize_all_tables()
        
        self.logger.info("数据库初始化完成")
    
    def initialize_data_components(self):
        """初始化数据组件"""
        self.logger.info("初始化数据组件...")
        
        # 数据采集器
        self.data_collector = LotteryDataCollector(
            db_path=self.config['database']['path'],
            sources=self.config['data_sources']
        )
        
        # 特征工程
        self.feature_engineer = FeatureEngineer(
            db_path=self.config['database']['path']
        )
        
        self.logger.info("数据组件初始化完成")
    
    def initialize_predictors(self):
        """初始化预测器"""
        self.logger.info("初始化预测器...")
        
        db_path = self.config['database']['path']
        
        # 位置预测器
        self.predictors['hundreds'] = EnsembleHundredsPredictor(db_path)
        self.predictors['tens'] = EnsembleTensPredictor(db_path)
        self.predictors['units'] = EnsembleUnitsPredictor(db_path)
        
        # 辅助预测器
        self.predictors['sum'] = EnsembleSumPredictor(db_path)
        self.predictors['span'] = EnsembleSpanPredictor(db_path)
        
        # 融合预测器
        self.fusion_predictor = FusionPredictor(db_path)
        
        self.logger.info("预测器初始化完成")
    
    def initialize_optimization(self):
        """初始化优化系统"""
        self.logger.info("初始化优化系统...")
        
        self.closed_loop_manager = ClosedLoopManager(
            db_path=self.config['database']['path']
        )
        
        # 设置预测器引用
        self.closed_loop_manager.optimizer.data_collector = self.data_collector
        self.closed_loop_manager.optimizer.model_trainers = self.predictors
        self.closed_loop_manager.optimizer.fusion_predictor = self.fusion_predictor
        
        self.logger.info("优化系统初始化完成")
    
    def initialize_web_app(self):
        """初始化Web应用"""
        self.logger.info("初始化Web应用...")
        
        self.web_app = create_app(
            config=self.config,
            prediction_system=self.fusion_predictor,
            closed_loop_manager=self.closed_loop_manager
        )
        
        self.logger.info("Web应用初始化完成")
    
    def collect_initial_data(self):
        """采集初始数据"""
        self.logger.info("开始采集历史数据...")
        
        try:
            # 采集历史数据
            records_count = self.data_collector.collect_historical_data()
            self.logger.info(f"成功采集 {records_count} 条历史数据")
            
            # 验证数据质量
            validation_result = self.data_collector.validate_all_data()
            self.logger.info(f"数据验证结果: {validation_result}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据采集失败: {e}")
            return False
    
    def generate_features(self):
        """生成特征"""
        self.logger.info("开始生成特征...")
        
        try:
            # 生成所有类型的特征
            features = self.feature_engineer.generate_all_features()
            
            # 保存特征到数据库
            self.feature_engineer.save_features_to_db(features)
            
            # 分析特征重要性
            for feature_type in ['hundreds', 'tens', 'units', 'sum', 'span']:
                importance = self.feature_engineer.analyze_feature_importance(feature_type)
                self.logger.info(f"{feature_type}特征重要性分析完成")
            
            return True
            
        except Exception as e:
            self.logger.error(f"特征生成失败: {e}")
            return False
    
    def train_all_models(self):
        """训练所有模型"""
        self.logger.info("开始训练所有模型...")
        
        training_results = {}
        
        try:
            # 训练各位置预测器
            for name, predictor in self.predictors.items():
                self.logger.info(f"训练 {name} 预测器...")
                result = predictor.train_all_models()
                training_results[name] = result
                self.logger.info(f"{name} 预测器训练完成")
            
            self.logger.info("所有模型训练完成")
            return training_results
            
        except Exception as e:
            self.logger.error(f"模型训练失败: {e}")
            return None
    
    def run_system_check(self):
        """运行系统检查"""
        self.logger.info("运行系统检查...")
        
        checks = {
            'database': self.check_database(),
            'data': self.check_data_integrity(),
            'models': self.check_models(),
            'features': self.check_features(),
            'optimization': self.check_optimization_system()
        }
        
        all_passed = all(checks.values())
        
        if all_passed:
            self.logger.info("系统检查全部通过")
        else:
            failed_checks = [k for k, v in checks.items() if not v]
            self.logger.warning(f"系统检查失败: {failed_checks}")
        
        return checks
    
    def check_database(self):
        """检查数据库"""
        try:
            return self.db_manager.check_database_integrity()
        except Exception as e:
            self.logger.error(f"数据库检查失败: {e}")
            return False
    
    def check_data_integrity(self):
        """检查数据完整性"""
        try:
            # 检查是否有足够的历史数据
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM lottery_data")
            count = cursor.fetchone()[0]
            conn.close()
            
            return count >= 1000  # 至少需要1000条历史数据
            
        except Exception as e:
            self.logger.error(f"数据完整性检查失败: {e}")
            return False
    
    def check_models(self):
        """检查模型状态"""
        try:
            for name, predictor in self.predictors.items():
                if not predictor.is_trained:
                    return False
            return True
            
        except Exception as e:
            self.logger.error(f"模型检查失败: {e}")
            return False
    
    def check_features(self):
        """检查特征状态"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # 检查各类型特征是否存在
            for feature_type in ['hundreds', 'tens', 'units', 'sum', 'span']:
                cursor.execute(
                    "SELECT COUNT(*) FROM feature_data WHERE feature_type = ?",
                    (feature_type,)
                )
                count = cursor.fetchone()[0]
                if count == 0:
                    conn.close()
                    return False
            
            conn.close()
            return True
            
        except Exception as e:
            self.logger.error(f"特征检查失败: {e}")
            return False
    
    def check_optimization_system(self):
        """检查优化系统"""
        try:
            return self.closed_loop_manager is not None
        except Exception as e:
            self.logger.error(f"优化系统检查失败: {e}")
            return False
    
    def start_web_server(self, host='127.0.0.1', port=5000, debug=False):
        """启动Web服务器"""
        self.logger.info(f"启动Web服务器 {host}:{port}")
        
        try:
            self.web_app.run(host=host, port=port, debug=debug)
        except Exception as e:
            self.logger.error(f"Web服务器启动失败: {e}")
            raise
    
    def start_optimization_loop(self):
        """启动优化循环"""
        self.logger.info("启动闭环优化系统...")
        
        try:
            result = self.closed_loop_manager.start_system()
            if result['status'] == 'success':
                self.logger.info("闭环优化系统启动成功")
                return True
            else:
                self.logger.error(f"闭环优化系统启动失败: {result}")
                return False
                
        except Exception as e:
            self.logger.error(f"优化循环启动失败: {e}")
            return False
    
    def stop_optimization_loop(self):
        """停止优化循环"""
        self.logger.info("停止闭环优化系统...")
        
        try:
            result = self.closed_loop_manager.stop_system()
            if result['status'] == 'success':
                self.logger.info("闭环优化系统停止成功")
                return True
            else:
                self.logger.error(f"闭环优化系统停止失败: {result}")
                return False
                
        except Exception as e:
            self.logger.error(f"优化循环停止失败: {e}")
            return False
    
    def generate_prediction(self, issue: str):
        """生成预测"""
        self.logger.info(f"生成期号 {issue} 的预测...")
        
        try:
            # 收集各预测器结果
            all_predictions = {}
            
            # 位置预测
            for name in ['hundreds', 'tens', 'units']:
                prediction = self.predictors[name].predict_next_period(issue)
                all_predictions[name] = prediction
            
            # 辅助预测
            sum_prediction = self.predictors['sum'].predict_next_period(
                issue, self.get_position_predictions(all_predictions)
            )
            span_prediction = self.predictors['span'].predict_next_period(
                issue, sum_prediction['predicted_sum'], 
                self.get_position_predictions(all_predictions)
            )
            
            all_predictions['sum'] = sum_prediction
            all_predictions['span'] = span_prediction
            
            # 融合预测
            final_result = self.fusion_predictor.predict_next_period(issue, all_predictions)
            
            self.logger.info(f"期号 {issue} 预测生成完成")
            return final_result
            
        except Exception as e:
            self.logger.error(f"预测生成失败: {e}")
            return None
    
    def get_position_predictions(self, all_predictions):
        """获取位置预测信息"""
        return {
            'hundreds_prob': all_predictions.get('hundreds', {}).get('probabilities'),
            'tens_prob': all_predictions.get('tens', {}).get('probabilities'),
            'units_prob': all_predictions.get('units', {}).get('probabilities')
        }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='福彩3D智能预测系统')
    parser.add_argument('--config', default='config/settings.yaml', help='配置文件路径')
    parser.add_argument('--mode', choices=['init', 'train', 'predict', 'web', 'check'], 
                       default='web', help='运行模式')
    parser.add_argument('--issue', help='预测期号 (predict模式使用)')
    parser.add_argument('--host', default='127.0.0.1', help='Web服务器地址')
    parser.add_argument('--port', type=int, default=5000, help='Web服务器端口')
    parser.add_argument('--debug', action='store_true', help='调试模式')
    
    args = parser.parse_args()
    
    # 创建系统实例
    system = LotteryPredictionSystem(args.config)
    
    try:
        if args.mode == 'init':
            # 初始化模式：设置数据库、采集数据、生成特征
            print("初始化系统...")
            system.initialize_database()
            system.initialize_data_components()
            
            if system.collect_initial_data():
                print("历史数据采集完成")
                if system.generate_features():
                    print("特征生成完成")
                    print("系统初始化完成！")
                else:
                    print("特征生成失败")
                    return 1
            else:
                print("数据采集失败")
                return 1
        
        elif args.mode == 'train':
            # 训练模式：训练所有模型
            print("训练模式...")
            system.initialize_database()
            system.initialize_data_components()
            system.initialize_predictors()
            
            results = system.train_all_models()
            if results:
                print("模型训练完成！")
                for name, result in results.items():
                    print(f"{name}: {result}")
            else:
                print("模型训练失败")
                return 1
        
        elif args.mode == 'predict':
            # 预测模式：生成单次预测
            if not args.issue:
                print("预测模式需要指定期号 (--issue)")
                return 1
            
            print(f"预测模式 - 期号: {args.issue}")
            system.initialize_database()
            system.initialize_data_components()
            system.initialize_predictors()
            
            result = system.generate_prediction(args.issue)
            if result:
                print("预测结果:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
            else:
                print("预测失败")
                return 1
        
        elif args.mode == 'check':
            # 检查模式：系统健康检查
            print("系统检查模式...")
            system.initialize_database()
            system.initialize_data_components()
            system.initialize_predictors()
            system.initialize_optimization()
            
            checks = system.run_system_check()
            print("系统检查结果:")
            for component, status in checks.items():
                status_text = "✓ 通过" if status else "✗ 失败"
                print(f"  {component}: {status_text}")
        
        elif args.mode == 'web':
            # Web模式：启动完整系统
            print("启动Web服务...")
            system.initialize_database()
            system.initialize_data_components()
            system.initialize_predictors()
            system.initialize_optimization()
            system.initialize_web_app()
            
            # 运行系统检查
            checks = system.run_system_check()
            if not all(checks.values()):
                print("警告：系统检查未全部通过，某些功能可能不可用")
            
            # 启动优化循环
            if system.start_optimization_loop():
                print("闭环优化系统已启动")
            else:
                print("警告：闭环优化系统启动失败")
            
            # 启动Web服务器
            print(f"Web服务器启动: http://{args.host}:{args.port}")
            system.start_web_server(args.host, args.port, args.debug)
    
    except KeyboardInterrupt:
        print("\n正在关闭系统...")
        if system.closed_loop_manager:
            system.stop_optimization_loop()
        print("系统已关闭")
        return 0
    
    except Exception as e:
        print(f"系统运行错误: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    import json
    sys.exit(main())
```

### 2. 配置管理 (src/utils/config.py)
```python
import yaml
import os
from pathlib import Path

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str):
        self.config_path = Path(config_path)
        self.config = self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        if not self.config_path.exists():
            self.create_default_config()
        
        with open(self.config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def create_default_config(self):
        """创建默认配置"""
        default_config = {
            'database': {
                'path': 'data/lottery.db',
                'backup_enabled': True,
                'backup_interval': 86400  # 24小时
            },
            'data_sources': {
                'primary': 'https://www.17500.cn/chart/3d-tjb.html',
                'backup': ['https://data.17500.cn/3d_desc.txt']
            },
            'logging': {
                'level': 'INFO',
                'file': 'logs/system.log',
                'max_size': '10MB',
                'backup_count': 5
            },
            'models': {
                'hundreds': {
                    'xgb_params': {
                        'n_estimators': 200,
                        'max_depth': 6,
                        'learning_rate': 0.1
                    }
                },
                'tens': {
                    'xgb_params': {
                        'n_estimators': 200,
                        'max_depth': 6,
                        'learning_rate': 0.1
                    }
                },
                'units': {
                    'xgb_params': {
                        'n_estimators': 200,
                        'max_depth': 6,
                        'learning_rate': 0.1
                    }
                }
            },
            'optimization': {
                'data_update_interval': 3600,
                'performance_check_interval': 1800,
                'retrain_threshold': 0.1
            },
            'web': {
                'host': '127.0.0.1',
                'port': 5000,
                'debug': False
            }
        }
        
        # 创建配置目录
        self.config_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(self.config_path, 'w', encoding='utf-8') as f:
            yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
        
        return default_config
    
    def get_config(self):
        """获取配置"""
        return self.config
    
    def update_config(self, updates):
        """更新配置"""
        self.config.update(updates)
        
        with open(self.config_path, 'w', encoding='utf-8') as f:
            yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
```

### 3. 部署脚本 (deploy.py)
```python
#!/usr/bin/env python3
"""
福彩3D预测系统部署脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 11):
        print("错误：需要Python 3.11或更高版本")
        return False
    print(f"✓ Python版本: {sys.version}")
    return True

def create_directories():
    """创建必要的目录"""
    directories = [
        'data',
        'logs',
        'config',
        'models',
        'backups',
        'reports'
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✓ 创建目录: {directory}")

def install_dependencies():
    """安装依赖包"""
    print("安装依赖包...")
    
    try:
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ])
        print("✓ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 依赖包安装失败: {e}")
        return False

def initialize_system():
    """初始化系统"""
    print("初始化系统...")
    
    try:
        subprocess.check_call([
            sys.executable, 'main.py', '--mode', 'init'
        ])
        print("✓ 系统初始化完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 系统初始化失败: {e}")
        return False

def train_models():
    """训练模型"""
    print("训练模型...")
    
    try:
        subprocess.check_call([
            sys.executable, 'main.py', '--mode', 'train'
        ])
        print("✓ 模型训练完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 模型训练失败: {e}")
        return False

def run_system_check():
    """运行系统检查"""
    print("运行系统检查...")
    
    try:
        subprocess.check_call([
            sys.executable, 'main.py', '--mode', 'check'
        ])
        print("✓ 系统检查完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 系统检查失败: {e}")
        return False

def create_startup_script():
    """创建启动脚本"""
    startup_script = """#!/bin/bash
# 福彩3D预测系统启动脚本

echo "启动福彩3D预测系统..."

# 激活虚拟环境（如果使用）
# source venv/bin/activate

# 启动系统
python main.py --mode web --host 127.0.0.1 --port 5000

echo "系统已停止"
"""
    
    with open('start.sh', 'w') as f:
        f.write(startup_script)
    
    os.chmod('start.sh', 0o755)
    print("✓ 启动脚本创建完成: start.sh")

def create_service_file():
    """创建系统服务文件"""
    service_content = f"""[Unit]
Description=福彩3D预测系统
After=network.target

[Service]
Type=simple
User=lottery
WorkingDirectory={os.getcwd()}
ExecStart={sys.executable} main.py --mode web
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
    
    with open('lottery-prediction.service', 'w') as f:
        f.write(service_content)
    
    print("✓ 系统服务文件创建完成: lottery-prediction.service")
    print("  安装服务: sudo cp lottery-prediction.service /etc/systemd/system/")
    print("  启动服务: sudo systemctl enable lottery-prediction && sudo systemctl start lottery-prediction")

def main():
    """主部署流程"""
    print("=" * 50)
    print("福彩3D预测系统部署脚本")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return 1
    
    # 创建目录
    create_directories()
    
    # 安装依赖
    if not install_dependencies():
        return 1
    
    # 初始化系统
    if not initialize_system():
        print("警告：系统初始化失败，请手动运行: python main.py --mode init")
    
    # 训练模型
    if not train_models():
        print("警告：模型训练失败，请手动运行: python main.py --mode train")
    
    # 系统检查
    if not run_system_check():
        print("警告：系统检查失败，某些功能可能不可用")
    
    # 创建启动脚本
    create_startup_script()
    create_service_file()
    
    print("\n" + "=" * 50)
    print("部署完成！")
    print("=" * 50)
    print("启动系统:")
    print("  方式1: ./start.sh")
    print("  方式2: python main.py --mode web")
    print("  方式3: 安装系统服务 (见上面的说明)")
    print("\n访问地址: http://127.0.0.1:5000")
    print("=" * 50)
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
```

### 4. 依赖文件 (requirements.txt)
```txt
# 核心依赖
numpy>=1.24.0
pandas>=2.0.0
scikit-learn>=1.3.0
scipy>=1.10.0

# 机器学习
xgboost>=2.0.0
lightgbm>=4.0.0
tensorflow>=2.13.0

# 数据采集
requests>=2.31.0
beautifulsoup4>=4.12.0

# Web框架
flask>=2.3.0
flask-socketio>=5.3.0

# 数据可视化
matplotlib>=3.7.0
plotly>=5.15.0

# 工具库
pyyaml>=6.0
schedule>=1.2.0
python-dateutil>=2.8.0

# 可选依赖
# huggingface-hub>=0.16.0  # 如果使用Hugging Face模型
# shap>=0.42.0             # 如果需要模型解释
```

### 5. 项目结构文档 (PROJECT_STRUCTURE.md)
```markdown
# 项目结构说明

## 目录结构
```
fucai3d-prediction/
├── main.py                     # 主程序入口
├── deploy.py                   # 部署脚本
├── requirements.txt            # 依赖列表
├── README.md                   # 项目说明
├── config/                     # 配置文件
│   └── settings.yaml          # 主配置文件
├── src/                       # 源代码
│   ├── data/                  # 数据处理
│   │   ├── collector.py       # 数据采集
│   │   ├── processor.py       # 数据预处理
│   │   └── feature_engine.py  # 特征工程
│   ├── models/                # 预测模型
│   │   ├── hundreds_predictor.py
│   │   ├── tens_predictor.py
│   │   ├── units_predictor.py
│   │   ├── sum_predictor.py
│   │   └── span_predictor.py
│   ├── prediction/            # 预测融合
│   │   └── fusion.py          # 智能融合
│   ├── optimization/          # 优化系统
│   │   └── closed_loop.py     # 闭环优化
│   ├── web/                   # Web界面
│   │   ├── app.py             # Flask应用
│   │   └── templates/         # 页面模板
│   └── utils/                 # 工具模块
│       ├── config.py          # 配置管理
│       ├── database.py        # 数据库操作
│       └── logger.py          # 日志管理
├── data/                      # 数据目录
│   └── lottery.db             # SQLite数据库
├── logs/                      # 日志目录
├── models/                    # 模型文件
├── backups/                   # 备份目录
└── reports/                   # 报告目录
```

## 运行模式

### 1. 初始化模式
```bash
python main.py --mode init
```
- 创建数据库表结构
- 采集历史数据
- 生成特征数据

### 2. 训练模式
```bash
python main.py --mode train
```
- 训练所有预测模型
- 保存模型文件

### 3. 预测模式
```bash
python main.py --mode predict --issue 2024001
```
- 生成指定期号的预测

### 4. Web模式
```bash
python main.py --mode web
```
- 启动完整的Web系统

### 5. 检查模式
```bash
python main.py --mode check
```
- 运行系统健康检查
```

## 成功标准

### 集成完整性
- [ ] 所有模块无缝集成
- [ ] 配置管理统一
- [ ] 错误处理完善
- [ ] 日志记录完整

### 部署便利性
- [ ] 一键部署脚本可用
- [ ] 依赖安装自动化
- [ ] 系统检查功能正常
- [ ] 启动脚本可用

### 系统稳定性
- [ ] 长时间运行稳定
- [ ] 异常自动恢复
- [ ] 资源使用合理
- [ ] 性能指标达标

## 部署说明

### 快速部署
```bash
# 1. 克隆项目
git clone <repository-url>
cd fucai3d-prediction

# 2. 运行部署脚本
python deploy.py

# 3. 启动系统
./start.sh
```

### 手动部署
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 初始化系统
python main.py --mode init

# 3. 训练模型
python main.py --mode train

# 4. 启动Web服务
python main.py --mode web
```

### 系统服务部署
```bash
# 1. 复制服务文件
sudo cp lottery-prediction.service /etc/systemd/system/

# 2. 启用并启动服务
sudo systemctl enable lottery-prediction
sudo systemctl start lottery-prediction

# 3. 查看服务状态
sudo systemctl status lottery-prediction
```

## 下一步
系统集成与部署完成后，福彩3D智能预测系统即可投入使用。建议定期进行系统维护和性能优化。
