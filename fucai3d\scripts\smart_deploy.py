#!/usr/bin/env python3
"""
智能部署脚本 - 优化版本
充分利用正序和倒序数据源的特点
"""

import sys
import os
import sqlite3
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_data_sources():
    """检查两个数据源的可用性 - 增强版本，处理限流"""
    print("🔍 检查数据源可用性...")

    import requests
    import time
    import random

    sources = {
        'asc': "https://data.17500.cn/3d_asc.txt",   # 正序
        'desc': "https://data.17500.cn/3d_desc.txt"  # 倒序
    }

    results = {}

    for name, url in sources.items():
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 添加随机延迟避免限流
                if attempt > 0:
                    delay = 30 + random.uniform(10, 30)  # 30-60秒延迟
                    print(f"  ⏳ 等待 {delay:.1f} 秒后重试 {name.upper()} 数据源...")
                    time.sleep(delay)

                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'text/plain, */*',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Connection': 'keep-alive',
                    'Cache-Control': 'no-cache'
                }

                response = requests.get(url, timeout=30, headers=headers)

                if response.status_code == 200:
                    content_length = len(response.text)
                    lines = len(response.text.split('\n'))
                    results[name] = {
                        'available': True,
                        'content_length': content_length,
                        'lines': lines,
                        'url': url
                    }
                    print(f"  ✅ {name.upper()}数据源可用: {content_length:,} 字符, {lines:,} 行")
                    break
                elif response.status_code == 429:
                    print(f"  ⚠️ {name.upper()}数据源限流 (HTTP 429)，尝试 {attempt + 1}/{max_retries}")
                    if attempt == max_retries - 1:
                        results[name] = {'available': False, 'error': f"HTTP {response.status_code} - 请求过于频繁"}
                        print(f"  ❌ {name.upper()}数据源不可用: 请求过于频繁，请稍后再试")
                else:
                    results[name] = {'available': False, 'error': f"HTTP {response.status_code}"}
                    print(f"  ❌ {name.upper()}数据源不可用: HTTP {response.status_code}")
                    break

            except Exception as e:
                if attempt == max_retries - 1:
                    results[name] = {'available': False, 'error': str(e)}
                    print(f"  ❌ {name.upper()}数据源不可用: {e}")
                else:
                    print(f"  ⚠️ {name.upper()}数据源连接失败，尝试 {attempt + 1}/{max_retries}: {e}")

    return results

def smart_complete_collection():
    """智能完整采集 - 使用正序数据源"""
    print("\n🚀 开始智能完整数据采集...")
    print("📊 策略：使用正序数据源(asc)进行完整采集，从历史到最新")
    
    try:
        from src.data.complete_collector import IntegratedCompleteCollector
        
        # 创建采集器
        collector = IntegratedCompleteCollector()
        
        # 执行完整采集
        success = collector.run_complete_collection()
        
        return success
        
    except Exception as e:
        print(f"❌ 智能完整采集失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def smart_incremental_update():
    """智能增量更新 - 使用倒序数据源"""
    print("\n🔄 开始智能增量更新...")
    print("📊 策略：使用倒序数据源(desc)进行增量更新，最新数据优先")
    
    try:
        from src.data.updater import smart_incremental_update
        
        # 执行智能增量更新（最多50条最新记录）
        success = smart_incremental_update(max_new_records=50)
        
        return success
        
    except Exception as e:
        print(f"❌ 智能增量更新失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def validate_database_quality():
    """验证数据库质量"""
    print("\n🔍 验证数据库质量...")
    
    db_path = 'data/lottery.db'
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='lottery_records'")
        if not cursor.fetchone():
            print("❌ lottery_records表不存在")
            conn.close()
            return False
        
        # 统计信息
        cursor.execute("SELECT COUNT(*) FROM lottery_records")
        total_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT MIN(period), MAX(period) FROM lottery_records")
        min_period, max_period = cursor.fetchone()
        
        cursor.execute("SELECT MIN(date), MAX(date) FROM lottery_records")
        min_date, max_date = cursor.fetchone()
        
        # 检查虚拟数据
        virtual_patterns = ['2024001', '2024002', '2024003']
        virtual_count = 0
        for pattern in virtual_patterns:
            cursor.execute("SELECT COUNT(*) FROM lottery_records WHERE period = ?", (pattern,))
            virtual_count += cursor.fetchone()[0]
        
        # 检查最新数据
        cursor.execute("SELECT period, date, numbers FROM lottery_records ORDER BY period DESC LIMIT 5")
        latest_records = cursor.fetchall()
        
        conn.close()
        
        # 显示结果
        print(f"📊 数据库质量报告:")
        print(f"  总记录数: {total_count:,} 条")
        print(f"  期号范围: {min_period} - {max_period}")
        print(f"  日期范围: {min_date} - {max_date}")
        print(f"  虚拟数据: {virtual_count} 条")
        
        print(f"\n📈 最新5条记录:")
        for i, (period, date, numbers) in enumerate(latest_records, 1):
            print(f"  {i}. 期号: {period}, 日期: {date}, 号码: {numbers}")
        
        # 质量评估
        quality_passed = True
        
        if total_count < 1000:
            print("❌ 数据量不足")
            quality_passed = False
        
        if virtual_count > 0:
            print("❌ 发现虚拟数据")
            quality_passed = False
        
        if not min_period or not min_period.startswith('200'):
            print("❌ 最早期号异常")
            quality_passed = False
        
        if quality_passed:
            print("✅ 数据库质量验证通过")
        else:
            print("❌ 数据库质量验证失败")
        
        return quality_passed
        
    except Exception as e:
        print(f"❌ 数据库验证失败: {e}")
        return False

def generate_optimization_report():
    """生成优化报告"""
    print("\n📋 生成优化报告...")
    
    report = f"""
# 福彩3D数据源优化部署报告

## 部署时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 数据源优化策略

### 双数据源配置
- **正序数据源**: https://data.17500.cn/3d_asc.txt
  - 用途: 完整数据采集
  - 优势: 从历史到最新的逻辑顺序
  - 适用场景: 初始化数据库、完整重建

- **倒序数据源**: https://data.17500.cn/3d_desc.txt
  - 用途: 增量数据更新
  - 优势: 最新数据在前，更新效率高
  - 适用场景: 日常更新、获取最新开奖

### 智能采集策略
1. **完整采集**: 使用正序数据源，确保数据的逻辑连续性
2. **增量更新**: 使用倒序数据源，优先获取最新数据
3. **容错机制**: 主备数据源自动切换，确保服务可用性

### 性能优化
- 增量更新限制最大记录数，避免过度处理
- 智能数据源选择，根据任务类型优化性能
- 数据去重和质量检查，确保数据完整性

## 部署结果
✅ 数据源优化配置完成
✅ 智能采集策略实施
✅ 质量验证机制就绪
✅ 容错和备份机制完善

## 使用建议
1. 日常使用智能增量更新: `python scripts/smart_deploy.py --incremental`
2. 完整重建使用完整采集: `python scripts/smart_deploy.py --complete`
3. 定期运行质量验证: `python scripts/smart_deploy.py --validate`

## 技术优势
- 充分利用两个数据源的特点
- 提高数据采集和更新效率
- 增强系统稳定性和可靠性
- 保持100%真实数据质量
"""
    
    # 保存报告
    try:
        os.makedirs('data', exist_ok=True)
        with open('data/optimization_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        print("✅ 优化报告已保存: data/optimization_report.md")
        return True
    except Exception as e:
        print(f"❌ 保存报告失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 福彩3D智能数据源优化部署")
    print("=" * 60)
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        mode = sys.argv[1]
        if mode == '--incremental':
            success = smart_incremental_update()
            sys.exit(0 if success else 1)
        elif mode == '--complete':
            success = smart_complete_collection()
            sys.exit(0 if success else 1)
        elif mode == '--validate':
            success = validate_database_quality()
            sys.exit(0 if success else 1)
    
    # 完整部署流程
    try:
        # 1. 检查数据源
        source_results = check_data_sources()
        
        available_sources = sum(1 for r in source_results.values() if r.get('available', False))
        if available_sources == 0:
            print("❌ 所有数据源都不可用，部署中止")
            return False
        
        print(f"✅ {available_sources}/2 个数据源可用")
        
        # 2. 执行智能完整采集
        if not smart_complete_collection():
            print("❌ 智能完整采集失败，部署中止")
            return False
        
        # 3. 验证数据库质量
        if not validate_database_quality():
            print("❌ 数据库质量验证失败，部署中止")
            return False
        
        # 4. 生成优化报告
        generate_optimization_report()
        
        print("\n" + "=" * 60)
        print("🎉 智能优化部署成功完成！")
        print("💾 数据库: data/lottery.db")
        print("📋 报告: data/optimization_report.md")
        print("🔄 增量更新: python scripts/smart_deploy.py --incremental")
        print("✅ 已实现数据源智能优化策略")
        
        return True
        
    except Exception as e:
        print(f"❌ 部署异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
