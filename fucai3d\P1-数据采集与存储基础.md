# P1-数据采集与存储基础

## 项目概述
**前置条件**：无  
**核心目标**：建立稳定的数据采集系统，获取完整历史数据  
**预计时间**：1-2周  

## 技术要求

### 数据源配置
- **主数据源**：https://www.17500.cn/chart/3d-tjb.html
- **备用数据源**：https://data.17500.cn/3d_desc.txt
- **采集范围**：第1期到最新期（8000+期）
- **反爬虫策略**：请求间隔、User-Agent轮换、异常重试

### 数据库设计

#### 核心数据表
```sql
-- 开奖数据表
CREATE TABLE lottery_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    issue TEXT UNIQUE NOT NULL,           -- 期号
    draw_date DATE NOT NULL,              -- 开奖日期
    hundreds INTEGER NOT NULL,            -- 百位数字
    tens INTEGER NOT NULL,                -- 十位数字
    units INTEGER NOT NULL,               -- 个位数字
    sum_value INTEGER NOT NULL,           -- 和值
    span INTEGER NOT NULL,                -- 跨度
    number_type TEXT,                     -- 号码类型（豹子、组三、组六）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 数据采集日志表
CREATE TABLE collection_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    source_url TEXT NOT NULL,
    collection_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status TEXT NOT NULL,                 -- success/failed/partial
    records_count INTEGER DEFAULT 0,
    error_message TEXT,
    response_time REAL
);

-- 数据验证表
CREATE TABLE data_validation (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    issue TEXT NOT NULL,
    validation_type TEXT NOT NULL,       -- format/logic/cross_check
    is_valid BOOLEAN NOT NULL,
    error_details TEXT,
    validated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 核心功能实现

### 1. 数据采集器
```python
import requests
from bs4 import BeautifulSoup
import time
import random
from typing import List, Dict
import sqlite3
from datetime import datetime

class LotteryDataCollector:
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.session = requests.Session()
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
        ]
        self.base_delay = 2  # 基础延迟2秒
        
    def get_random_headers(self) -> Dict[str, str]:
        return {
            'User-Agent': random.choice(self.user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    
    def safe_request(self, url: str, max_retries: int = 3) -> requests.Response:
        """安全的HTTP请求，包含重试和延迟机制"""
        for attempt in range(max_retries):
            try:
                # 随机延迟，避免被封IP
                delay = self.base_delay + random.uniform(0, 2)
                time.sleep(delay)
                
                headers = self.get_random_headers()
                response = self.session.get(url, headers=headers, timeout=30)
                
                if response.status_code == 200:
                    return response
                elif response.status_code == 429:  # 请求过于频繁
                    wait_time = 60 + random.uniform(0, 30)
                    print(f"请求频率过高，等待{wait_time:.1f}秒...")
                    time.sleep(wait_time)
                    continue
                else:
                    print(f"HTTP错误: {response.status_code}")
                    
            except Exception as e:
                print(f"请求失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(10 * (attempt + 1))  # 递增延迟
                    
        raise Exception(f"请求失败，已重试{max_retries}次")
    
    def parse_lottery_data(self, html_content: str) -> List[Dict]:
        """解析彩票数据"""
        soup = BeautifulSoup(html_content, 'html.parser')
        data_list = []
        
        # 根据实际网页结构解析数据
        # 这里需要根据具体网页结构来实现
        table = soup.find('table', class_='chart_table')
        if not table:
            raise Exception("未找到数据表格")
            
        rows = table.find_all('tr')[1:]  # 跳过表头
        
        for row in rows:
            cells = row.find_all('td')
            if len(cells) >= 4:
                try:
                    issue = cells[0].text.strip()
                    draw_date = cells[1].text.strip()
                    numbers = cells[2].text.strip()
                    
                    # 解析三位数字
                    if len(numbers) == 3 and numbers.isdigit():
                        hundreds = int(numbers[0])
                        tens = int(numbers[1])
                        units = int(numbers[2])
                        sum_value = hundreds + tens + units
                        span = max(hundreds, tens, units) - min(hundreds, tens, units)
                        
                        # 判断号码类型
                        if hundreds == tens == units:
                            number_type = "豹子"
                        elif hundreds == tens or tens == units or hundreds == units:
                            number_type = "组三"
                        else:
                            number_type = "组六"
                        
                        data_list.append({
                            'issue': issue,
                            'draw_date': draw_date,
                            'hundreds': hundreds,
                            'tens': tens,
                            'units': units,
                            'sum_value': sum_value,
                            'span': span,
                            'number_type': number_type
                        })
                        
                except (ValueError, IndexError) as e:
                    print(f"解析数据错误: {e}, 行数据: {[cell.text.strip() for cell in cells]}")
                    continue
                    
        return data_list
    
    def collect_historical_data(self) -> int:
        """采集历史数据"""
        url = "https://www.17500.cn/chart/3d-tjb.html"
        
        try:
            print("开始采集历史数据...")
            response = self.safe_request(url)
            data_list = self.parse_lottery_data(response.text)
            
            # 保存到数据库
            saved_count = self.save_to_database(data_list)
            
            # 记录采集日志
            self.log_collection(url, "success", saved_count, None, response.elapsed.total_seconds())
            
            print(f"成功采集并保存 {saved_count} 条数据")
            return saved_count
            
        except Exception as e:
            error_msg = str(e)
            print(f"采集失败: {error_msg}")
            self.log_collection(url, "failed", 0, error_msg, 0)
            return 0
    
    def save_to_database(self, data_list: List[Dict]) -> int:
        """保存数据到数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        saved_count = 0
        for data in data_list:
            try:
                cursor.execute("""
                    INSERT OR REPLACE INTO lottery_data 
                    (issue, draw_date, hundreds, tens, units, sum_value, span, number_type)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    data['issue'], data['draw_date'], data['hundreds'], 
                    data['tens'], data['units'], data['sum_value'], 
                    data['span'], data['number_type']
                ))
                saved_count += 1
            except sqlite3.Error as e:
                print(f"保存数据错误: {e}, 数据: {data}")
                
        conn.commit()
        conn.close()
        return saved_count
    
    def log_collection(self, url: str, status: str, count: int, error: str, response_time: float):
        """记录采集日志"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO collection_logs 
            (source_url, status, records_count, error_message, response_time)
            VALUES (?, ?, ?, ?, ?)
        """, (url, status, count, error, response_time))
        
        conn.commit()
        conn.close()
```

### 2. 数据验证器
```python
class DataValidator:
    def __init__(self, db_path: str):
        self.db_path = db_path
    
    def validate_all_data(self) -> Dict[str, int]:
        """验证所有数据"""
        results = {
            'total': 0,
            'valid': 0,
            'invalid': 0,
            'format_errors': 0,
            'logic_errors': 0
        }
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM lottery_data ORDER BY issue")
        rows = cursor.fetchall()
        
        for row in rows:
            results['total'] += 1
            is_valid = True
            error_details = []
            
            # 格式验证
            if not self.validate_format(row):
                results['format_errors'] += 1
                is_valid = False
                error_details.append("格式错误")
            
            # 逻辑验证
            if not self.validate_logic(row):
                results['logic_errors'] += 1
                is_valid = False
                error_details.append("逻辑错误")
            
            if is_valid:
                results['valid'] += 1
            else:
                results['invalid'] += 1
                
            # 记录验证结果
            self.log_validation(row[1], "comprehensive", is_valid, "; ".join(error_details))
        
        conn.close()
        return results
    
    def validate_format(self, row) -> bool:
        """格式验证"""
        try:
            hundreds, tens, units = row[3], row[4], row[5]
            return (0 <= hundreds <= 9 and 
                   0 <= tens <= 9 and 
                   0 <= units <= 9)
        except:
            return False
    
    def validate_logic(self, row) -> bool:
        """逻辑验证"""
        try:
            hundreds, tens, units, sum_value, span = row[3], row[4], row[5], row[6], row[7]
            
            # 验证和值
            calculated_sum = hundreds + tens + units
            if calculated_sum != sum_value:
                return False
            
            # 验证跨度
            calculated_span = max(hundreds, tens, units) - min(hundreds, tens, units)
            if calculated_span != span:
                return False
                
            return True
        except:
            return False
    
    def log_validation(self, issue: str, validation_type: str, is_valid: bool, error_details: str):
        """记录验证日志"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO data_validation 
            (issue, validation_type, is_valid, error_details)
            VALUES (?, ?, ?, ?)
        """, (issue, validation_type, is_valid, error_details))
        
        conn.commit()
        conn.close()
```

### 3. 数据库初始化
```python
def init_database(db_path: str):
    """初始化数据库"""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建表
    cursor.executescript("""
        CREATE TABLE IF NOT EXISTS lottery_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            issue TEXT UNIQUE NOT NULL,
            draw_date DATE NOT NULL,
            hundreds INTEGER NOT NULL,
            tens INTEGER NOT NULL,
            units INTEGER NOT NULL,
            sum_value INTEGER NOT NULL,
            span INTEGER NOT NULL,
            number_type TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE TABLE IF NOT EXISTS collection_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            source_url TEXT NOT NULL,
            collection_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status TEXT NOT NULL,
            records_count INTEGER DEFAULT 0,
            error_message TEXT,
            response_time REAL
        );
        
        CREATE TABLE IF NOT EXISTS data_validation (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            issue TEXT NOT NULL,
            validation_type TEXT NOT NULL,
            is_valid BOOLEAN NOT NULL,
            error_details TEXT,
            validated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE INDEX IF NOT EXISTS idx_lottery_issue ON lottery_data(issue);
        CREATE INDEX IF NOT EXISTS idx_lottery_date ON lottery_data(draw_date);
    """)
    
    conn.commit()
    conn.close()
    print("数据库初始化完成")
```

## 成功标准

### 数据完整性
- [ ] 成功采集8000+期历史数据
- [ ] 数据格式验证通过率>99%
- [ ] 数据逻辑验证通过率>99%

### 系统稳定性
- [ ] 数据采集不被封IP
- [ ] 异常情况自动重试成功
- [ ] 采集日志完整记录

### 性能指标
- [ ] 单次采集响应时间<30秒
- [ ] 数据保存成功率>99%
- [ ] 系统运行稳定无崩溃

## 部署说明

### 环境要求
```bash
pip install requests beautifulsoup4 sqlite3
```

### 运行步骤
```python
# 1. 初始化数据库
init_database("data/lottery.db")

# 2. 创建采集器
collector = LotteryDataCollector("data/lottery.db")

# 3. 采集历史数据
count = collector.collect_historical_data()

# 4. 验证数据
validator = DataValidator("data/lottery.db")
results = validator.validate_all_data()
print(f"验证结果: {results}")
```

## 下一步
完成P1后，进入**P2-特征工程系统**开发阶段。
