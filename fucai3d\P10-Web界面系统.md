# P10-Web界面系统

## 项目概述
**前置条件**：P9-闭环自动优化系统完成  
**核心目标**：实现美观实用的Web界面  
**预计时间**：2-3周  

## 技术要求

### 界面目标
- **美观设计**：现代化响应式界面
- **功能完整**：涵盖所有核心功能
- **用户友好**：直观的操作体验
- **实时更新**：动态数据展示
- **性能优化**：快速响应和加载

### 技术栈选择
- **后端框架**：Flask 2.3+
- **前端技术**：HTML5 + CSS3 + JavaScript (ES6+)
- **UI框架**：Bootstrap 5 + Chart.js
- **实时通信**：WebSocket (Flask-SocketIO)
- **数据可视化**：Chart.js + D3.js
- **图标库**：Font Awesome

### 页面结构
```
顶部导航栏：
├── 数据采集状态
├── 模型预测
├── 历史复盘
├── 性能监控
└── 系统设置

主要页面：
1. 预测结果页面
2. 历史复盘页面
3. 实时监控页面
4. 系统管理页面
```

## 核心功能实现

### 1. Flask应用主体
```python
from flask import Flask, render_template, request, jsonify, session
from flask_socketio import SocketIO, emit
import sqlite3
import json
import pandas as pd
from datetime import datetime, timedelta
import threading
import time

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
socketio = SocketIO(app, cors_allowed_origins="*")

# 全局变量
prediction_system = None
closed_loop_manager = None

def init_prediction_system():
    """初始化预测系统"""
    global prediction_system, closed_loop_manager
    
    try:
        from src.prediction.fusion import FusionPredictor
        from src.optimization.closed_loop import ClosedLoopManager
        
        prediction_system = FusionPredictor("data/lottery.db")
        closed_loop_manager = ClosedLoopManager("data/lottery.db")
        
        print("预测系统初始化完成")
    except Exception as e:
        print(f"预测系统初始化失败: {e}")

@app.route('/')
def index():
    """主页 - 预测结果展示"""
    return render_template('index.html')

@app.route('/api/latest_prediction')
def get_latest_prediction():
    """获取最新预测结果"""
    try:
        conn = sqlite3.connect("data/lottery.db")
        
        # 获取最新预测
        query = """
            SELECT issue, prediction_rank, hundreds, tens, units, sum_value, span,
                   combined_probability, confidence_level, constraint_score
            FROM final_predictions
            WHERE issue = (SELECT MAX(issue) FROM final_predictions)
            ORDER BY prediction_rank
            LIMIT 20
        """
        
        predictions = pd.read_sql_query(query, conn)
        
        # 获取各位置概率分布
        position_probs = get_position_probabilities(conn)
        
        # 获取和值跨度预测
        auxiliary_predictions = get_auxiliary_predictions(conn)
        
        conn.close()
        
        return jsonify({
            'status': 'success',
            'predictions': predictions.to_dict('records'),
            'position_probabilities': position_probs,
            'auxiliary_predictions': auxiliary_predictions,
            'update_time': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

def get_position_probabilities(conn):
    """获取位置概率分布"""
    # 获取最新的百位、十位、个位预测概率
    latest_issue_query = "SELECT MAX(issue) FROM hundreds_predictions"
    latest_issue = pd.read_sql_query(latest_issue_query, conn).iloc[0, 0]
    
    if not latest_issue:
        return {}
    
    # 百位概率
    hundreds_query = """
        SELECT prob_0, prob_1, prob_2, prob_3, prob_4, prob_5, prob_6, prob_7, prob_8, prob_9
        FROM hundreds_predictions
        WHERE issue = ? AND model_type = 'ensemble'
    """
    hundreds_prob = pd.read_sql_query(hundreds_query, conn, params=(latest_issue,))
    
    # 十位概率
    tens_query = """
        SELECT prob_0, prob_1, prob_2, prob_3, prob_4, prob_5, prob_6, prob_7, prob_8, prob_9
        FROM tens_predictions
        WHERE issue = ? AND model_type = 'ensemble'
    """
    tens_prob = pd.read_sql_query(tens_query, conn, params=(latest_issue,))
    
    # 个位概率
    units_query = """
        SELECT prob_0, prob_1, prob_2, prob_3, prob_4, prob_5, prob_6, prob_7, prob_8, prob_9
        FROM units_predictions
        WHERE issue = ? AND model_type = 'ensemble'
    """
    units_prob = pd.read_sql_query(units_query, conn, params=(latest_issue,))
    
    return {
        'hundreds': hundreds_prob.iloc[0].tolist() if not hundreds_prob.empty else [0.1] * 10,
        'tens': tens_prob.iloc[0].tolist() if not tens_prob.empty else [0.1] * 10,
        'units': units_prob.iloc[0].tolist() if not units_prob.empty else [0.1] * 10
    }

def get_auxiliary_predictions(conn):
    """获取和值跨度预测"""
    latest_issue_query = "SELECT MAX(issue) FROM sum_predictions"
    latest_issue = pd.read_sql_query(latest_issue_query, conn).iloc[0, 0]
    
    if not latest_issue:
        return {}
    
    # 和值预测
    sum_query = """
        SELECT predicted_sum, confidence, prediction_range_min, prediction_range_max
        FROM sum_predictions
        WHERE issue = ? AND model_type = 'ensemble'
    """
    sum_pred = pd.read_sql_query(sum_query, conn, params=(latest_issue,))
    
    # 跨度预测
    span_query = """
        SELECT predicted_span, confidence, prediction_range_min, prediction_range_max
        FROM span_predictions
        WHERE issue = ? AND model_type = 'ensemble'
    """
    span_pred = pd.read_sql_query(span_query, conn, params=(latest_issue,))
    
    return {
        'sum': sum_pred.iloc[0].to_dict() if not sum_pred.empty else {},
        'span': span_pred.iloc[0].to_dict() if not span_pred.empty else {}
    }

@app.route('/api/generate_prediction', methods=['POST'])
def generate_prediction():
    """生成新的预测"""
    try:
        data = request.get_json()
        issue = data.get('issue')
        
        if not issue:
            return jsonify({'status': 'error', 'message': '缺少期号参数'})
        
        if prediction_system is None:
            return jsonify({'status': 'error', 'message': '预测系统未初始化'})
        
        # 获取各预测器结果
        all_predictions = collect_all_predictions()
        
        # 执行融合预测
        result = prediction_system.predict_next_period(issue, all_predictions)
        
        # 通过WebSocket广播更新
        socketio.emit('prediction_updated', result)
        
        return jsonify({'status': 'success', 'result': result})
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

def collect_all_predictions():
    """收集所有预测器的结果"""
    # 这里应该调用各个预测器获取最新预测
    # 为简化示例，返回模拟数据
    return {
        'hundreds': {'probabilities': [0.1, 0.2, 0.15, 0.1, 0.05, 0.1, 0.1, 0.05, 0.1, 0.05]},
        'tens': {'probabilities': [0.05, 0.1, 0.2, 0.15, 0.1, 0.1, 0.1, 0.05, 0.1, 0.05]},
        'units': {'probabilities': [0.08, 0.12, 0.18, 0.12, 0.08, 0.12, 0.12, 0.08, 0.05, 0.05]},
        'sum': {'predicted_sum': 15.5},
        'span': {'predicted_span': 6.2}
    }

@app.route('/history')
def history():
    """历史复盘页面"""
    return render_template('history.html')

@app.route('/api/prediction_history')
def get_prediction_history():
    """获取预测历史"""
    try:
        days = request.args.get('days', 30, type=int)
        
        conn = sqlite3.connect("data/lottery.db")
        
        query = """
            SELECT pp.issue, pp.actual_hundreds, pp.actual_tens, pp.actual_units,
                   pp.actual_sum, pp.actual_span, pp.predicted_rank, pp.hit_type,
                   pp.overall_score, pp.evaluated_at,
                   fp.hundreds as pred_hundreds, fp.tens as pred_tens, fp.units as pred_units,
                   fp.combined_probability, fp.confidence_level
            FROM prediction_performance pp
            LEFT JOIN final_predictions fp ON pp.issue = fp.issue AND fp.prediction_rank = 1
            WHERE pp.evaluated_at >= datetime('now', '-{} days')
            ORDER BY pp.evaluated_at DESC
        """.format(days)
        
        history_data = pd.read_sql_query(query, conn)
        
        # 计算统计信息
        stats = calculate_prediction_stats(history_data)
        
        conn.close()
        
        return jsonify({
            'status': 'success',
            'history': history_data.to_dict('records'),
            'statistics': stats
        })
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

def calculate_prediction_stats(history_data):
    """计算预测统计信息"""
    if history_data.empty:
        return {}
    
    total_predictions = len(history_data)
    exact_hits = len(history_data[history_data['hit_type'] == 'exact'])
    position_hits = len(history_data[history_data['hit_type'] == 'position'])
    
    # 各位置准确率
    hundreds_accuracy = (history_data['actual_hundreds'] == history_data['pred_hundreds']).mean()
    tens_accuracy = (history_data['actual_tens'] == history_data['pred_tens']).mean()
    units_accuracy = (history_data['actual_units'] == history_data['pred_units']).mean()
    
    # 平均排名
    valid_ranks = history_data[history_data['predicted_rank'].notna()]['predicted_rank']
    avg_rank = valid_ranks.mean() if not valid_ranks.empty else None
    
    return {
        'total_predictions': total_predictions,
        'exact_hit_rate': exact_hits / total_predictions,
        'position_hit_rate': position_hits / total_predictions,
        'hundreds_accuracy': hundreds_accuracy,
        'tens_accuracy': tens_accuracy,
        'units_accuracy': units_accuracy,
        'average_rank': avg_rank,
        'average_score': history_data['overall_score'].mean()
    }

@app.route('/monitor')
def monitor():
    """实时监控页面"""
    return render_template('monitor.html')

@app.route('/api/system_status')
def get_system_status():
    """获取系统状态"""
    try:
        if closed_loop_manager is None:
            return jsonify({'status': 'error', 'message': '闭环管理器未初始化'})
        
        status = closed_loop_manager.get_system_status()
        
        # 获取数据采集状态
        data_status = get_data_collection_status()
        
        # 获取模型性能
        model_performance = get_model_performance()
        
        return jsonify({
            'status': 'success',
            'system_status': status,
            'data_status': data_status,
            'model_performance': model_performance,
            'update_time': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

def get_data_collection_status():
    """获取数据采集状态"""
    conn = sqlite3.connect("data/lottery.db")
    
    # 最近的采集记录
    query = """
        SELECT collection_time, status, records_count, error_message
        FROM collection_logs
        ORDER BY collection_time DESC
        LIMIT 10
    """
    
    logs = pd.read_sql_query(query, conn)
    
    # 数据完整性检查
    data_count_query = "SELECT COUNT(*) as total_records FROM lottery_data"
    total_records = pd.read_sql_query(data_count_query, conn).iloc[0, 0]
    
    conn.close()
    
    return {
        'recent_logs': logs.to_dict('records'),
        'total_records': total_records,
        'last_update': logs.iloc[0]['collection_time'] if not logs.empty else None
    }

def get_model_performance():
    """获取模型性能"""
    conn = sqlite3.connect("data/lottery.db")
    
    # 获取最近的性能监控数据
    query = """
        SELECT component_name, performance_metric, current_value, threshold_value, status
        FROM system_performance_monitor
        WHERE monitor_time >= datetime('now', '-1 day')
        ORDER BY monitor_time DESC
    """
    
    performance_data = pd.read_sql_query(query, conn)
    
    conn.close()
    
    # 按组件分组
    performance_by_component = {}
    for _, row in performance_data.iterrows():
        component = row['component_name']
        if component not in performance_by_component:
            performance_by_component[component] = {}
        
        performance_by_component[component][row['performance_metric']] = {
            'current_value': row['current_value'],
            'threshold_value': row['threshold_value'],
            'status': row['status']
        }
    
    return performance_by_component

@app.route('/settings')
def settings():
    """系统设置页面"""
    return render_template('settings.html')

@app.route('/api/optimization_config')
def get_optimization_config():
    """获取优化配置"""
    try:
        if closed_loop_manager is None:
            return jsonify({'status': 'error', 'message': '闭环管理器未初始化'})
        
        config = closed_loop_manager.optimizer.optimization_config
        thresholds = closed_loop_manager.optimizer.performance_thresholds
        
        return jsonify({
            'status': 'success',
            'optimization_config': config,
            'performance_thresholds': thresholds
        })
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/api/manual_optimization', methods=['POST'])
def manual_optimization():
    """手动触发优化"""
    try:
        data = request.get_json()
        optimization_type = data.get('type')
        component = data.get('component')
        
        if closed_loop_manager is None:
            return jsonify({'status': 'error', 'message': '闭环管理器未初始化'})
        
        result = closed_loop_manager.manual_trigger_optimization(optimization_type, component)
        
        # 通过WebSocket广播状态更新
        socketio.emit('optimization_status', result)
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

# WebSocket事件处理
@socketio.on('connect')
def handle_connect():
    """客户端连接"""
    print('客户端已连接')
    emit('connected', {'message': '连接成功'})

@socketio.on('disconnect')
def handle_disconnect():
    """客户端断开连接"""
    print('客户端已断开连接')

@socketio.on('request_status_update')
def handle_status_request():
    """请求状态更新"""
    try:
        status = get_system_status()
        emit('status_update', status)
    except Exception as e:
        emit('error', {'message': str(e)})

# 后台任务：定期推送状态更新
def background_status_update():
    """后台状态更新任务"""
    while True:
        try:
            if closed_loop_manager and closed_loop_manager.optimizer.is_running:
                status = get_system_status()
                socketio.emit('status_update', status)
        except Exception as e:
            print(f"状态更新失败: {e}")
        
        time.sleep(30)  # 每30秒更新一次

# 启动后台任务
def start_background_tasks():
    """启动后台任务"""
    status_thread = threading.Thread(target=background_status_update, daemon=True)
    status_thread.start()

if __name__ == '__main__':
    # 初始化系统
    init_prediction_system()
    start_background_tasks()
    
    # 启动Flask应用
    socketio.run(app, host='127.0.0.1', port=5000, debug=True)
```

### 2. HTML模板

#### 基础模板 (templates/base.html)
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}福彩3D智能预测系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Socket.IO -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    
    <style>
        .navbar-brand {
            font-weight: bold;
            color: #007bff !important;
        }
        
        .prediction-card {
            border-left: 4px solid #007bff;
            transition: transform 0.2s;
        }
        
        .prediction-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .probability-bar {
            height: 20px;
            background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-normal { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-critical { background-color: #dc3545; }
        
        .number-display {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
            text-align: center;
            padding: 10px;
            border: 2px solid #007bff;
            border-radius: 8px;
            margin: 5px;
            min-width: 60px;
        }
        
        .confidence-high { border-color: #28a745; color: #28a745; }
        .confidence-medium { border-color: #ffc107; color: #ffc107; }
        .confidence-low { border-color: #dc3545; color: #dc3545; }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>
                福彩3D智能预测系统
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home me-1"></i>预测结果
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/history">
                            <i class="fas fa-history me-1"></i>历史复盘
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/monitor">
                            <i class="fas fa-tachometer-alt me-1"></i>实时监控
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/settings">
                            <i class="fas fa-cog me-1"></i>系统设置
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="navbar-text">
                            <span class="status-indicator status-normal" id="systemStatus"></span>
                            系统运行正常
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container mt-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 全局JavaScript -->
    <script>
        // Socket.IO连接
        const socket = io();
        
        socket.on('connect', function() {
            console.log('WebSocket连接成功');
        });
        
        socket.on('status_update', function(data) {
            updateSystemStatus(data);
        });
        
        socket.on('prediction_updated', function(data) {
            if (typeof updatePredictionDisplay === 'function') {
                updatePredictionDisplay(data);
            }
        });
        
        function updateSystemStatus(data) {
            const statusIndicator = document.getElementById('systemStatus');
            if (statusIndicator) {
                statusIndicator.className = 'status-indicator status-normal';
            }
        }
        
        // 工具函数
        function formatNumber(num, decimals = 2) {
            return parseFloat(num).toFixed(decimals);
        }
        
        function formatPercentage(num) {
            return (num * 100).toFixed(1) + '%';
        }
        
        function getConfidenceClass(level) {
            switch(level) {
                case 'high': return 'confidence-high';
                case 'medium': return 'confidence-medium';
                case 'low': return 'confidence-low';
                default: return '';
            }
        }
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
```

#### 主页模板 (templates/index.html)
```html
{% extends "base.html" %}

{% block title %}预测结果 - 福彩3D智能预测系统{% endblock %}

{% block content %}
<div class="row">
    <!-- 最新预测结果 -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-crystal-ball me-2"></i>最新预测结果
                </h5>
                <button class="btn btn-primary btn-sm" onclick="generateNewPrediction()">
                    <i class="fas fa-sync-alt me-1"></i>生成预测
                </button>
            </div>
            <div class="card-body">
                <div id="predictionResults">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载预测结果...</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 推荐号码列表 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list-ol me-2"></i>推荐号码 (Top 10)
                </h5>
            </div>
            <div class="card-body">
                <div id="recommendationsList">
                    <!-- 动态加载推荐列表 -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- 侧边栏 -->
    <div class="col-lg-4">
        <!-- 位置概率分布 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>位置概率分布
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">百位</label>
                    <canvas id="hundredsChart" height="100"></canvas>
                </div>
                <div class="mb-3">
                    <label class="form-label">十位</label>
                    <canvas id="tensChart" height="100"></canvas>
                </div>
                <div class="mb-3">
                    <label class="form-label">个位</label>
                    <canvas id="unitsChart" height="100"></canvas>
                </div>
            </div>
        </div>
        
        <!-- 和值跨度预测 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-calculator me-2"></i>和值跨度预测
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border rounded p-3">
                            <h4 class="text-primary mb-1" id="predictedSum">--</h4>
                            <small class="text-muted">预测和值</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-3">
                            <h4 class="text-success mb-1" id="predictedSpan">--</h4>
                            <small class="text-muted">预测跨度</small>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted">和值范围:</small>
                            <div id="sumRange">--</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">跨度范围:</small>
                            <div id="spanRange">--</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let positionCharts = {};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    loadLatestPrediction();
    
    // 每30秒自动刷新
    setInterval(loadLatestPrediction, 30000);
});

function initializeCharts() {
    // 初始化位置概率图表
    const chartConfig = {
        type: 'bar',
        data: {
            labels: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'],
            datasets: [{
                data: [0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1],
                backgroundColor: 'rgba(0, 123, 255, 0.6)',
                borderColor: 'rgba(0, 123, 255, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 1,
                    ticks: {
                        callback: function(value) {
                            return formatPercentage(value);
                        }
                    }
                }
            }
        }
    };
    
    positionCharts.hundreds = new Chart(document.getElementById('hundredsChart'), chartConfig);
    positionCharts.tens = new Chart(document.getElementById('tensChart'), {...chartConfig});
    positionCharts.units = new Chart(document.getElementById('unitsChart'), {...chartConfig});
}

function loadLatestPrediction() {
    fetch('/api/latest_prediction')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                updatePredictionDisplay(data);
            } else {
                showError('加载预测结果失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('网络错误，请稍后重试');
        });
}

function updatePredictionDisplay(data) {
    // 更新主要预测结果
    updateMainPrediction(data.predictions[0]);
    
    // 更新推荐列表
    updateRecommendationsList(data.predictions);
    
    // 更新位置概率图表
    updatePositionCharts(data.position_probabilities);
    
    // 更新和值跨度
    updateAuxiliaryPredictions(data.auxiliary_predictions);
}

function updateMainPrediction(prediction) {
    const html = `
        <div class="text-center mb-4">
            <h2 class="text-primary mb-3">最佳推荐号码</h2>
            <div class="d-flex justify-content-center align-items-center">
                <div class="number-display ${getConfidenceClass(prediction.confidence_level)}">
                    ${prediction.hundreds}
                </div>
                <div class="number-display ${getConfidenceClass(prediction.confidence_level)}">
                    ${prediction.tens}
                </div>
                <div class="number-display ${getConfidenceClass(prediction.confidence_level)}">
                    ${prediction.units}
                </div>
            </div>
            <div class="mt-3">
                <span class="badge bg-primary me-2">和值: ${prediction.sum_value}</span>
                <span class="badge bg-success me-2">跨度: ${prediction.span}</span>
                <span class="badge bg-info">置信度: ${prediction.confidence_level}</span>
            </div>
            <div class="mt-2">
                <small class="text-muted">
                    综合概率: ${formatPercentage(prediction.combined_probability)} | 
                    约束分数: ${formatNumber(prediction.constraint_score)}
                </small>
            </div>
        </div>
    `;
    
    document.getElementById('predictionResults').innerHTML = html;
}

function updateRecommendationsList(predictions) {
    const html = predictions.slice(0, 10).map((pred, index) => `
        <div class="prediction-card card mb-2">
            <div class="card-body py-2">
                <div class="row align-items-center">
                    <div class="col-1">
                        <span class="badge bg-secondary">${index + 1}</span>
                    </div>
                    <div class="col-3">
                        <strong>${pred.hundreds}${pred.tens}${pred.units}</strong>
                    </div>
                    <div class="col-2">
                        <small>和值: ${pred.sum_value}</small>
                    </div>
                    <div class="col-2">
                        <small>跨度: ${pred.span}</small>
                    </div>
                    <div class="col-2">
                        <small>${formatPercentage(pred.combined_probability)}</small>
                    </div>
                    <div class="col-2">
                        <span class="badge bg-${pred.confidence_level === 'high' ? 'success' : pred.confidence_level === 'medium' ? 'warning' : 'danger'}">
                            ${pred.confidence_level}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
    
    document.getElementById('recommendationsList').innerHTML = html;
}

function updatePositionCharts(probabilities) {
    if (probabilities.hundreds) {
        positionCharts.hundreds.data.datasets[0].data = probabilities.hundreds;
        positionCharts.hundreds.update();
    }
    
    if (probabilities.tens) {
        positionCharts.tens.data.datasets[0].data = probabilities.tens;
        positionCharts.tens.update();
    }
    
    if (probabilities.units) {
        positionCharts.units.data.datasets[0].data = probabilities.units;
        positionCharts.units.update();
    }
}

function updateAuxiliaryPredictions(auxiliary) {
    if (auxiliary.sum) {
        document.getElementById('predictedSum').textContent = formatNumber(auxiliary.sum.predicted_sum, 1);
        document.getElementById('sumRange').textContent = 
            `${auxiliary.sum.prediction_range_min} - ${auxiliary.sum.prediction_range_max}`;
    }
    
    if (auxiliary.span) {
        document.getElementById('predictedSpan').textContent = formatNumber(auxiliary.span.predicted_span, 1);
        document.getElementById('spanRange').textContent = 
            `${auxiliary.span.prediction_range_min} - ${auxiliary.span.prediction_range_max}`;
    }
}

function generateNewPrediction() {
    const issue = prompt('请输入期号 (格式: 2024001):');
    if (!issue) return;
    
    const button = event.target;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>生成中...';
    
    fetch('/api/generate_prediction', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ issue: issue })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showSuccess('预测生成成功！');
            loadLatestPrediction();
        } else {
            showError('预测生成失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('网络错误，请稍后重试');
    })
    .finally(() => {
        button.disabled = false;
        button.innerHTML = '<i class="fas fa-sync-alt me-1"></i>生成预测';
    });
}

function showSuccess(message) {
    // 显示成功消息的实现
    alert(message); // 简化实现，实际应该使用更好的通知组件
}

function showError(message) {
    // 显示错误消息的实现
    alert(message); // 简化实现，实际应该使用更好的通知组件
}
</script>
{% endblock %}
```

## 成功标准

### 界面质量
- [ ] 界面美观现代化
- [ ] 响应式设计适配各种屏幕
- [ ] 用户体验流畅直观
- [ ] 加载速度快 (<3秒)

### 功能完整性
- [ ] 预测结果实时展示
- [ ] 历史数据分析完整
- [ ] 系统监控功能正常
- [ ] 设置管理功能可用

### 技术指标
- [ ] WebSocket实时通信正常
- [ ] 图表展示准确美观
- [ ] 数据更新及时
- [ ] 错误处理完善

## 部署说明

```bash
# 安装依赖
pip install flask flask-socketio

# 创建模板目录
mkdir -p templates static/css static/js

# 启动应用
python app.py

# 访问地址
http://127.0.0.1:5000
```

## 下一步
完成P10后，进入**P11-系统集成与部署**开发阶段。
