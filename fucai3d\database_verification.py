#!/usr/bin/env python3
"""
数据库验证脚本
验证数据库中是否包含2002001到2025204的所有真实数据
"""

import sqlite3
import os
from datetime import datetime

def verify_database_completeness():
    """验证数据库完整性"""
    db_path = "data/lottery.db"
    
    print("🔍 开始验证数据库完整性...")
    print(f"📍 数据库路径: {db_path}")
    
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"📊 数据库中的表: {[table[0] for table in tables]}")
        
        # 检查lottery_data表
        if ('lottery_data',) in tables:
            table_name = 'lottery_data'
        elif ('lottery_records',) in tables:
            table_name = 'lottery_records'
        else:
            print("❌ 未找到彩票数据表")
            conn.close()
            return False
        
        print(f"✅ 找到数据表: {table_name}")
        
        # 获取基本统计信息
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        total_count = cursor.fetchone()[0]
        
        if total_count == 0:
            print("❌ 数据表为空，需要执行数据采集")
            conn.close()
            return False
        
        # 获取期号范围
        if table_name == 'lottery_data':
            cursor.execute(f"SELECT MIN(issue), MAX(issue) FROM {table_name}")
            min_issue, max_issue = cursor.fetchone()
            
            cursor.execute(f"SELECT MIN(draw_date), MAX(draw_date) FROM {table_name}")
            min_date, max_date = cursor.fetchone()
            
            # 检查关键期号
            cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE issue = '2002001'")
            has_start = cursor.fetchone()[0] > 0
            
            cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE issue = '2025204'")
            has_end = cursor.fetchone()[0] > 0
            
            # 检查虚拟数据
            virtual_patterns = ['2024001', '2024002', '2024003']
            virtual_count = 0
            for pattern in virtual_patterns:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE issue = ?", (pattern,))
                virtual_count += cursor.fetchone()[0]
            
            # 获取最新数据样例
            cursor.execute(f"SELECT issue, draw_date, hundreds, tens, units FROM {table_name} ORDER BY issue DESC LIMIT 5")
            latest_records = cursor.fetchall()
            
        else:  # lottery_records
            cursor.execute(f"SELECT MIN(period), MAX(period) FROM {table_name}")
            min_issue, max_issue = cursor.fetchone()
            
            cursor.execute(f"SELECT MIN(date), MAX(date) FROM {table_name}")
            min_date, max_date = cursor.fetchone()
            
            # 检查关键期号
            cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE period = '2002001'")
            has_start = cursor.fetchone()[0] > 0
            
            cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE period = '2025204'")
            has_end = cursor.fetchone()[0] > 0
            
            # 检查虚拟数据
            virtual_patterns = ['2024001', '2024002', '2024003']
            virtual_count = 0
            for pattern in virtual_patterns:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE period = ?", (pattern,))
                virtual_count += cursor.fetchone()[0]
            
            # 获取最新数据样例
            cursor.execute(f"SELECT period, date, numbers FROM {table_name} ORDER BY period DESC LIMIT 5")
            latest_records = cursor.fetchall()
        
        conn.close()
        
        # 显示验证结果
        print(f"\n📊 数据库验证结果:")
        print(f"  总记录数: {total_count:,} 条")
        print(f"  期号范围: {min_issue} - {max_issue}")
        print(f"  日期范围: {min_date} - {max_date}")
        print(f"  包含起始期号(2002001): {'✅' if has_start else '❌'}")
        print(f"  包含目标期号(2025204): {'✅' if has_end else '❌'}")
        print(f"  虚拟数据: {virtual_count} 条")
        
        print(f"\n📈 最新5条记录:")
        for i, record in enumerate(latest_records, 1):
            if table_name == 'lottery_data':
                issue, date, h, t, u = record
                print(f"  {i}. 期号: {issue}, 日期: {date}, 号码: {h}{t}{u}")
            else:
                period, date, numbers = record
                print(f"  {i}. 期号: {period}, 日期: {date}, 号码: {numbers}")
        
        # 评估完整性
        completeness_score = 0
        issues = []
        
        if total_count >= 8000:
            completeness_score += 25
        else:
            issues.append(f"数据量不足: {total_count} < 8000")
        
        if has_start:
            completeness_score += 25
        else:
            issues.append("缺少起始期号 2002001")
        
        if has_end:
            completeness_score += 25
        else:
            issues.append("缺少目标期号 2025204")
        
        if virtual_count == 0:
            completeness_score += 25
        else:
            issues.append(f"发现虚拟数据: {virtual_count} 条")
        
        print(f"\n🎯 完整性评估:")
        print(f"  完整性分数: {completeness_score}/100")
        
        if completeness_score == 100:
            print("  ✅ 数据库完整性验证通过")
            print("  ✅ 包含2002001-2025204所有真实数据")
            return True
        else:
            print("  ❌ 数据库完整性验证失败")
            print("  🔧 发现的问题:")
            for issue in issues:
                print(f"    - {issue}")
            return False
        
    except Exception as e:
        print(f"❌ 数据库验证失败: {e}")
        return False

def recommend_actions():
    """推荐修复操作"""
    print(f"\n💡 建议操作:")
    print(f"1. 执行完整数据采集:")
    print(f"   python scripts/smart_deploy.py --complete")
    print(f"2. 执行增量更新:")
    print(f"   python scripts/smart_deploy.py --incremental")
    print(f"3. 重新验证数据库:")
    print(f"   python database_verification.py")

if __name__ == "__main__":
    print("🚀 福彩3D数据库完整性验证")
    print("=" * 60)
    
    success = verify_database_completeness()
    
    if not success:
        recommend_actions()
    
    print(f"\n{'✅ 验证完成' if success else '❌ 验证失败'}")
