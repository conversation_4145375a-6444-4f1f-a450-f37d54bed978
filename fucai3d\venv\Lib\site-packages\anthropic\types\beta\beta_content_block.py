# File generated from our OpenAPI spec by <PERSON><PERSON><PERSON>. See CONTRIBUTING.md for details.

from typing import Union
from typing_extensions import Annotated, TypeAlias

from ..._utils import PropertyIn<PERSON>
from .beta_text_block import <PERSON><PERSON>ext<PERSON><PERSON>
from .beta_thinking_block import <PERSON>Thinking<PERSON><PERSON>
from .beta_tool_use_block import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .beta_mcp_tool_use_block import <PERSON><PERSON>P<PERSON>ool<PERSON>se<PERSON><PERSON>
from .beta_mcp_tool_result_block import <PERSON>MCPToolResult<PERSON>lock
from .beta_server_tool_use_block import BetaServerToolUse<PERSON>lock
from .beta_container_upload_block import Beta<PERSON>ontainerU<PERSON>load<PERSON><PERSON>
from .beta_redacted_thinking_block import BetaRedactedThinking<PERSON>lock
from .beta_web_search_tool_result_block import <PERSON>WebSearch<PERSON><PERSON>Result<PERSON>lock
from .beta_code_execution_tool_result_block import BetaCodeExecutionToolResultBlock

__all__ = ["BetaContentBlock"]

BetaContentBlock: TypeAlias = Annotated[
    Union[
        BetaTextBlock,
        BetaThinkingBlock,
        BetaRedactedThinking<PERSON>lock,
        BetaTool<PERSON><PERSON><PERSON><PERSON>,
        BetaServerToolUse<PERSON>lock,
        BetaWebSearchToolResult<PERSON>lock,
        BetaCodeExecutionToolResultBlock,
        BetaMCPToolUseBlock,
        BetaMCPToolResultBlock,
        BetaContainerUploadBlock,
    ],
    PropertyInfo(discriminator="type"),
]
