"""
跨度专用特征生成器
为跨度预测器提供20+维专用特征，包括：
- 分布特征：跨度分布区间、密度分析
- 统计特征：均值、方差、频次分析
- 变化特征：跨度变化趋势、稳定性
- 关联特征：与和值的关系、组合特性
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from collections import Counter


def generate_span_features(df: pd.DataFrame, window_sizes: List[int] = [5, 10, 20, 50]) -> pd.DataFrame:
    """
    生成跨度专用特征
    
    Args:
        df: 包含历史数据的DataFrame，必须包含'span'列或能计算跨度的列
        window_sizes: 滑动窗口大小列表
        
    Returns:
        pd.DataFrame: 包含跨度专用特征的DataFrame
    """
    result_df = df.copy()
    
    # 确保有跨度列
    if 'span' not in df.columns:
        if all(col in df.columns for col in ['hundreds', 'tens', 'units']):
            max_vals = df[['hundreds', 'tens', 'units']].max(axis=1)
            min_vals = df[['hundreds', 'tens', 'units']].min(axis=1)
            result_df['span'] = max_vals - min_vals
        else:
            raise ValueError("DataFrame必须包含'span'列或'hundreds', 'tens', 'units'列")
    
    span_series = result_df['span']
    
    # 1. 分布区间特征
    result_df.update(_generate_span_distribution_features(span_series, window_sizes))
    
    # 2. 统计分析特征
    result_df.update(_generate_span_statistical_features(span_series, window_sizes))
    
    # 3. 变化趋势特征
    result_df.update(_generate_span_change_features(span_series, window_sizes))
    
    # 4. 频次分析特征
    result_df.update(_generate_span_frequency_features(span_series, window_sizes))
    
    # 5. 关联性特征（如果存在和值数据）
    if 'sum_value' in df.columns or all(col in df.columns for col in ['hundreds', 'tens', 'units']):
        if 'sum_value' not in result_df.columns:
            result_df['sum_value'] = df['hundreds'] + df['tens'] + df['units']
        result_df.update(_generate_span_correlation_features(result_df[['span', 'sum_value']], window_sizes))
    
    return result_df


def _generate_span_distribution_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成跨度分布特征"""
    features = pd.DataFrame(index=series.index)
    
    # 跨度分布区间定义
    # 福彩3D跨度范围：0-9
    def get_span_zone(value):
        if value <= 3:
            return 'small'
        elif value <= 6:
            return 'medium'
        else:
            return 'large'
    
    def get_detailed_zone(value):
        if value == 0:
            return 'zero'
        elif value <= 2:
            return 'very_small'
        elif value <= 4:
            return 'small'
        elif value <= 6:
            return 'medium'
        elif value <= 8:
            return 'large'
        else:
            return 'very_large'
    
    # 基础分布特征
    features['span_zone'] = series.apply(get_span_zone)
    features['span_detailed_zone'] = series.apply(get_detailed_zone)
    features['span_is_zero'] = (series == 0).astype(int)
    features['span_is_small'] = (series <= 3).astype(int)
    features['span_is_medium'] = ((series > 3) & (series <= 6)).astype(int)
    features['span_is_large'] = (series > 6).astype(int)
    
    for window in window_sizes:
        # 分布区间统计
        features[f'span_zero_ratio_{window}'] = features['span_is_zero'].rolling(window=window).mean()
        features[f'span_small_ratio_{window}'] = features['span_is_small'].rolling(window=window).mean()
        features[f'span_medium_ratio_{window}'] = features['span_is_medium'].rolling(window=window).mean()
        features[f'span_large_ratio_{window}'] = features['span_is_large'].rolling(window=window).mean()
        
        # 分布密度
        features[f'span_distribution_density_{window}'] = _calculate_span_density(series, window)
        
        # 分布集中度
        features[f'span_concentration_{window}'] = _calculate_span_concentration(series, window)
    
    return features


def _generate_span_statistical_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成跨度统计特征"""
    features = pd.DataFrame(index=series.index)
    
    for window in window_sizes:
        # 基础统计量
        features[f'span_mean_{window}'] = series.rolling(window=window).mean()
        features[f'span_std_{window}'] = series.rolling(window=window).std()
        features[f'span_var_{window}'] = series.rolling(window=window).var()
        features[f'span_range_{window}'] = series.rolling(window=window).max() - series.rolling(window=window).min()
        
        # 分位数
        features[f'span_q25_{window}'] = series.rolling(window=window).quantile(0.25)
        features[f'span_median_{window}'] = series.rolling(window=window).median()
        features[f'span_q75_{window}'] = series.rolling(window=window).quantile(0.75)
        features[f'span_iqr_{window}'] = features[f'span_q75_{window}'] - features[f'span_q25_{window}']
        
        # 分布形状
        features[f'span_skew_{window}'] = series.rolling(window=window).skew()
        features[f'span_kurt_{window}'] = series.rolling(window=window).kurt()
        
        # 众数
        features[f'span_mode_{window}'] = series.rolling(window=window).apply(
            lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else x.iloc[-1]
        )
        
        # 与统计量的关系
        features[f'span_above_mean_{window}'] = (series > features[f'span_mean_{window}']).astype(int)
        features[f'span_distance_from_mean_{window}'] = np.abs(series - features[f'span_mean_{window}'])
        features[f'span_distance_from_median_{window}'] = np.abs(series - features[f'span_median_{window}'])
    
    return features


def _generate_span_change_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成跨度变化特征"""
    features = pd.DataFrame(index=series.index)
    
    # 基础变化特征
    features['span_change'] = series.diff()
    features['span_change_abs'] = np.abs(features['span_change'])
    features['span_change_direction'] = np.sign(features['span_change'])
    
    # 变化幅度分类
    features['span_no_change'] = (features['span_change'] == 0).astype(int)
    features['span_small_change'] = (features['span_change_abs'] == 1).astype(int)
    features['span_large_change'] = (features['span_change_abs'] > 1).astype(int)
    
    for window in window_sizes:
        # 变化统计
        features[f'span_change_mean_{window}'] = features['span_change'].rolling(window=window).mean()
        features[f'span_change_std_{window}'] = features['span_change'].rolling(window=window).std()
        features[f'span_change_abs_mean_{window}'] = features['span_change_abs'].rolling(window=window).mean()
        
        # 变化方向统计
        features[f'span_increasing_ratio_{window}'] = (features['span_change'] > 0).rolling(window=window).mean()
        features[f'span_decreasing_ratio_{window}'] = (features['span_change'] < 0).rolling(window=window).mean()
        features[f'span_stable_ratio_{window}'] = (features['span_change'] == 0).rolling(window=window).mean()
        
        # 变化幅度统计
        features[f'span_no_change_ratio_{window}'] = features['span_no_change'].rolling(window=window).mean()
        features[f'span_small_change_ratio_{window}'] = features['span_small_change'].rolling(window=window).mean()
        features[f'span_large_change_ratio_{window}'] = features['span_large_change'].rolling(window=window).mean()
        
        # 变化稳定性
        features[f'span_change_stability_{window}'] = 1 / (1 + features[f'span_change_std_{window}'])
        
        # 变化趋势
        features[f'span_change_trend_{window}'] = features['span_change'].rolling(window=window).apply(
            lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) > 1 else 0
        )
    
    return features


def _generate_span_frequency_features(series: pd.Series, window_sizes: List[int]) -> pd.DataFrame:
    """生成跨度频次特征"""
    features = pd.DataFrame(index=series.index)
    
    # 全局频次特征
    global_counts = series.value_counts()
    features['span_global_freq'] = series.map(global_counts)
    features['span_global_freq_rank'] = features['span_global_freq'].rank(pct=True)
    
    for window in window_sizes:
        # 滑动窗口频次统计
        freq_counts = series.rolling(window=window, min_periods=1).apply(
            lambda x: Counter(x)[x.iloc[-1]] if len(x) > 0 else 0
        )
        features[f'span_freq_{window}'] = freq_counts
        
        # 频次排名
        features[f'span_freq_rank_{window}'] = freq_counts.rolling(window=window).rank(pct=True)
        
        # 频次变化率
        features[f'span_freq_change_{window}'] = freq_counts.pct_change()
        
        # 热度指标
        expected_freq = window / 10  # 10个可能的跨度值
        features[f'span_hotness_{window}'] = (freq_counts - expected_freq) / expected_freq
        
        # 连续出现
        features[f'span_consecutive_{window}'] = _calculate_span_consecutive(series, window)
        
        # 遗漏分析
        features[f'span_missing_{window}'] = _calculate_span_missing(series, window)
    
    return features


def _generate_span_correlation_features(df: pd.DataFrame, window_sizes: List[int]) -> pd.DataFrame:
    """生成跨度关联性特征"""
    features = pd.DataFrame(index=df.index)
    
    span = df['span']
    sum_value = df['sum_value']
    
    for window in window_sizes:
        # 与和值的相关性
        features[f'span_sum_corr_{window}'] = span.rolling(window=window).corr(sum_value)
        
        # 跨度和值比值
        features[f'span_sum_ratio_{window}'] = (span / (sum_value + 1e-8)).rolling(window=window).mean()
        
        # 跨度和值差值
        features[f'span_sum_diff_{window}'] = (span - sum_value).rolling(window=window).mean()
        
        # 跨度和值组合模式
        features[f'span_sum_pattern_{window}'] = _calculate_span_sum_pattern(span, sum_value, window)
        
        # 跨度对和值的影响
        features[f'span_sum_influence_{window}'] = _calculate_span_influence(span, sum_value, window)
    
    return features


# 辅助函数
def _calculate_span_density(series: pd.Series, window: int) -> pd.Series:
    """计算跨度分布密度"""
    densities = []
    for i in range(len(series)):
        if i < window:
            densities.append(0)
        else:
            window_data = series.iloc[i-window+1:i+1]
            unique_count = len(window_data.unique())
            # 跨度有10个可能值（0-9）
            density = unique_count / 10.0
            densities.append(density)
    return pd.Series(densities, index=series.index)


def _calculate_span_concentration(series: pd.Series, window: int) -> pd.Series:
    """计算跨度集中度"""
    concentrations = []
    for i in range(len(series)):
        if i < window:
            concentrations.append(0)
        else:
            window_data = series.iloc[i-window+1:i+1]
            value_counts = window_data.value_counts()
            max_freq = value_counts.max()
            concentration = max_freq / window
            concentrations.append(concentration)
    return pd.Series(concentrations, index=series.index)


def _calculate_span_consecutive(series: pd.Series, window: int) -> pd.Series:
    """计算跨度连续出现次数"""
    consecutive_counts = []
    for i in range(len(series)):
        if i == 0:
            consecutive_counts.append(1)
        else:
            if series.iloc[i] == series.iloc[i-1]:
                consecutive_counts.append(consecutive_counts[-1] + 1)
            else:
                consecutive_counts.append(1)
    
    consecutive_series = pd.Series(consecutive_counts, index=series.index)
    return consecutive_series.rolling(window=window).max()


def _calculate_span_missing(series: pd.Series, window: int) -> pd.Series:
    """计算跨度遗漏次数"""
    missing_counts = []
    for i in range(len(series)):
        if i == 0:
            missing_counts.append(0)
        else:
            current_value = series.iloc[i]
            missing = 0
            for j in range(i-1, max(-1, i-window), -1):
                if series.iloc[j] == current_value:
                    break
                missing += 1
            missing_counts.append(missing)
    
    return pd.Series(missing_counts, index=series.index)


def _calculate_span_sum_pattern(span: pd.Series, sum_value: pd.Series, window: int) -> pd.Series:
    """计算跨度和值组合模式"""
    patterns = []
    for i in range(len(span)):
        if i < window:
            patterns.append(0)
        else:
            window_span = span.iloc[i-window+1:i+1]
            window_sum = sum_value.iloc[i-window+1:i+1]
            
            # 计算组合的唯一性
            combinations = list(zip(window_span, window_sum))
            unique_combinations = len(set(combinations))
            pattern_score = unique_combinations / window
            patterns.append(pattern_score)
    
    return pd.Series(patterns, index=span.index)


def _calculate_span_influence(span: pd.Series, sum_value: pd.Series, window: int) -> pd.Series:
    """计算跨度对和值的影响"""
    influences = []
    for i in range(len(span)):
        if i < window:
            influences.append(0)
        else:
            window_span = span.iloc[i-window+1:i+1]
            window_sum = sum_value.iloc[i-window+1:i+1]
            
            # 计算跨度变化对和值变化的影响
            span_changes = window_span.diff().dropna()
            sum_changes = window_sum.diff().dropna()
            
            if len(span_changes) > 1 and len(sum_changes) > 1:
                correlation = span_changes.corr(sum_changes)
                influence = correlation if not pd.isna(correlation) else 0
            else:
                influence = 0
            
            influences.append(influence)
    
    return pd.Series(influences, index=span.index)
