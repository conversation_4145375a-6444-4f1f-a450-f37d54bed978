#!/usr/bin/env python3
"""
P1模块全面测试套件
测试数据采集、解析、验证、更新等所有核心功能
"""

import unittest
import sys
import os
import tempfile
import sqlite3
from pathlib import Path

# 添加src目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / 'src'))


class TestDatabaseModels(unittest.TestCase):
    """测试数据库模型"""
    
    def setUp(self):
        """测试前准备"""
        self.test_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.test_db.close()
        self.db_path = self.test_db.name
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
    
    def test_database_creation(self):
        """测试数据库创建"""
        # 创建数据库
        conn = sqlite3.connect(self.db_path)
        
        # 创建表
        conn.execute("""
        CREATE TABLE lottery_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            issue TEXT UNIQUE NOT NULL,
            draw_date TEXT NOT NULL,
            hundreds INTEGER NOT NULL,
            tens INTEGER NOT NULL,
            units INTEGER NOT NULL,
            sum_value INTEGER,
            span INTEGER,
            number_type TEXT
        )
        """)
        
        # 插入测试数据
        conn.execute("""
        INSERT INTO lottery_data (issue, draw_date, hundreds, tens, units, sum_value, span, number_type)
        VALUES ('2024001', '2024-01-01', 1, 2, 3, 6, 2, '组六')
        """)
        
        conn.commit()
        
        # 验证数据
        cursor = conn.execute("SELECT COUNT(*) FROM lottery_data")
        count = cursor.fetchone()[0]
        self.assertEqual(count, 1)
        
        conn.close()
    
    def test_data_validation(self):
        """测试数据验证"""
        # 测试有效数据
        valid_data = {
            'issue': '2024001',
            'draw_date': '2024-01-01',
            'hundreds': 1,
            'tens': 2,
            'units': 3
        }
        
        # 基本验证
        self.assertTrue(len(valid_data['issue']) == 7)
        self.assertTrue(valid_data['issue'].isdigit())
        self.assertTrue(0 <= valid_data['hundreds'] <= 9)
        self.assertTrue(0 <= valid_data['tens'] <= 9)
        self.assertTrue(0 <= valid_data['units'] <= 9)
        
        # 测试无效数据
        invalid_data = {
            'issue': 'invalid',
            'hundreds': 10,  # 超出范围
            'tens': -1,      # 超出范围
            'units': 'a'     # 类型错误
        }
        
        self.assertFalse(invalid_data['issue'].isdigit())
        self.assertFalse(0 <= invalid_data['hundreds'] <= 9)
        self.assertFalse(0 <= invalid_data['tens'] <= 9)


class TestDataParser(unittest.TestCase):
    """测试数据解析器"""
    
    def test_text_parsing(self):
        """测试文本解析"""
        # 模拟文本数据
        test_text = "2024001,2024-01-01,123"
        parts = test_text.split(',')
        
        # 验证解析结果
        self.assertEqual(len(parts), 3)
        self.assertEqual(parts[0], '2024001')
        self.assertEqual(parts[1], '2024-01-01')
        self.assertEqual(parts[2], '123')
        
        # 解析号码
        numbers = [int(d) for d in parts[2]]
        self.assertEqual(numbers, [1, 2, 3])
        self.assertEqual(sum(numbers), 6)  # 和值
        self.assertEqual(max(numbers) - min(numbers), 2)  # 跨度
    
    def test_number_type_detection(self):
        """测试号码类型判断"""
        def determine_type(numbers):
            unique_count = len(set(numbers))
            if unique_count == 1:
                return "豹子"
            elif unique_count == 2:
                return "对子"
            else:
                return "组六"
        
        # 测试各种类型
        self.assertEqual(determine_type([1, 1, 1]), "豹子")
        self.assertEqual(determine_type([1, 1, 2]), "对子")
        self.assertEqual(determine_type([1, 2, 3]), "组六")


class TestDataCollector(unittest.TestCase):
    """测试数据采集器"""
    
    def test_url_validation(self):
        """测试URL验证"""
        valid_urls = [
            "https://www.17500.cn/chart/3d-tjb.html",
            "https://data.17500.cn/3d_asc.txt"
        ]
        
        for url in valid_urls:
            self.assertTrue(url.startswith('http'))
            self.assertTrue('17500.cn' in url)
    
    def test_anti_crawler_config(self):
        """测试反爬虫配置"""
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        ]
        
        # 验证User-Agent配置
        self.assertTrue(len(user_agents) > 0)
        for ua in user_agents:
            self.assertTrue('Mozilla' in ua)
        
        # 验证延迟配置
        base_delay = 2
        max_delay = 10
        self.assertTrue(base_delay > 0)
        self.assertTrue(max_delay > base_delay)


class TestDataValidator(unittest.TestCase):
    """测试数据验证器"""
    
    def test_format_validation(self):
        """测试格式验证"""
        # 测试期号格式
        valid_issue = "2024001"
        invalid_issue = "invalid"
        
        self.assertTrue(len(valid_issue) == 7 and valid_issue.isdigit())
        self.assertFalse(len(invalid_issue) == 7 and invalid_issue.isdigit())
        
        # 测试日期格式
        import re
        valid_date = "2024-01-01"
        invalid_date = "2024/13/32"
        
        date_pattern = r'^\d{4}-\d{2}-\d{2}$'
        self.assertTrue(re.match(date_pattern, valid_date))
        self.assertFalse(re.match(date_pattern, invalid_date))
    
    def test_logic_validation(self):
        """测试逻辑验证"""
        # 测试和值计算
        numbers = [1, 2, 3]
        expected_sum = 6
        actual_sum = sum(numbers)
        self.assertEqual(expected_sum, actual_sum)
        
        # 测试跨度计算
        expected_span = 2
        actual_span = max(numbers) - min(numbers)
        self.assertEqual(expected_span, actual_span)


class TestIncrementalUpdater(unittest.TestCase):
    """测试增量更新器"""
    
    def setUp(self):
        """测试前准备"""
        self.test_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.test_db.close()
        self.db_path = self.test_db.name
        
        # 创建测试数据库
        conn = sqlite3.connect(self.db_path)
        conn.execute("""
        CREATE TABLE lottery_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            issue TEXT UNIQUE NOT NULL,
            draw_date TEXT NOT NULL,
            hundreds INTEGER NOT NULL,
            tens INTEGER NOT NULL,
            units INTEGER NOT NULL,
            sum_value INTEGER,
            span INTEGER,
            number_type TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        # 插入初始数据
        conn.execute("""
        INSERT INTO lottery_data (issue, draw_date, hundreds, tens, units, sum_value, span, number_type)
        VALUES ('2024001', '2024-01-01', 1, 2, 3, 6, 2, '组六')
        """)
        conn.commit()
        conn.close()
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
    
    def test_get_latest_issue(self):
        """测试获取最新期号"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        
        cursor = conn.execute(
            "SELECT issue FROM lottery_data ORDER BY issue DESC LIMIT 1"
        )
        result = cursor.fetchone()
        latest_issue = result['issue'] if result else None
        
        self.assertEqual(latest_issue, '2024001')
        conn.close()
    
    def test_record_insertion(self):
        """测试记录插入"""
        new_record = {
            'issue': '2024002',
            'draw_date': '2024-01-02',
            'hundreds': 4,
            'tens': 5,
            'units': 6,
            'sum_value': 15,
            'span': 2,
            'number_type': '组六'
        }
        
        conn = sqlite3.connect(self.db_path)
        
        # 插入新记录
        conn.execute("""
        INSERT INTO lottery_data (issue, draw_date, hundreds, tens, units, sum_value, span, number_type)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            new_record['issue'],
            new_record['draw_date'],
            new_record['hundreds'],
            new_record['tens'],
            new_record['units'],
            new_record['sum_value'],
            new_record['span'],
            new_record['number_type']
        ))
        conn.commit()
        
        # 验证插入
        cursor = conn.execute("SELECT COUNT(*) FROM lottery_data")
        count = cursor.fetchone()[0]
        self.assertEqual(count, 2)
        
        conn.close()


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def test_end_to_end_workflow(self):
        """测试端到端工作流程"""
        # 1. 模拟数据采集
        collected_data = [
            {
                'issue': '2024001',
                'draw_date': '2024-01-01',
                'hundreds': 1,
                'tens': 2,
                'units': 3
            }
        ]
        
        # 2. 数据解析和计算
        for record in collected_data:
            numbers = [record['hundreds'], record['tens'], record['units']]
            record['sum_value'] = sum(numbers)
            record['span'] = max(numbers) - min(numbers)
            
            unique_count = len(set(numbers))
            if unique_count == 1:
                record['number_type'] = "豹子"
            elif unique_count == 2:
                record['number_type'] = "对子"
            else:
                record['number_type'] = "组六"
        
        # 3. 数据验证
        for record in collected_data:
            self.assertTrue(len(record['issue']) == 7)
            self.assertTrue(record['issue'].isdigit())
            self.assertTrue(0 <= record['hundreds'] <= 9)
            self.assertTrue(0 <= record['tens'] <= 9)
            self.assertTrue(0 <= record['units'] <= 9)
            self.assertTrue(0 <= record['sum_value'] <= 27)
            self.assertTrue(0 <= record['span'] <= 9)
            self.assertIn(record['number_type'], ['豹子', '对子', '组六'])
        
        # 4. 数据存储（模拟）
        self.assertEqual(len(collected_data), 1)
        self.assertEqual(collected_data[0]['issue'], '2024001')


def run_all_tests():
    """运行所有测试"""
    print("=== P1模块全面测试开始 ===")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestDatabaseModels,
        TestDataParser,
        TestDataCollector,
        TestDataValidator,
        TestIncrementalUpdater,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    print(f"\n=== 测试结果摘要 ===")
    print(f"运行测试: {result.testsRun}")
    print(f"失败测试: {len(result.failures)}")
    print(f"错误测试: {len(result.errors)}")
    print(f"成功率: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print(f"\n失败详情:")
        for test, traceback in result.failures:
            print(f"  {test}: {traceback}")
    
    if result.errors:
        print(f"\n错误详情:")
        for test, traceback in result.errors:
            print(f"  {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    print(f"\n🎉 测试{'通过' if success else '失败'}！")
    
    return success


if __name__ == "__main__":
    run_all_tests()
