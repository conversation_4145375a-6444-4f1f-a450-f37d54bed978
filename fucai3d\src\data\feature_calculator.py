"""
福彩3D特征计算器
基于现有开奖号码实时计算各种特征，无需修改数据库结构
"""

from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass


@dataclass
class FeatureResult:
    """特征计算结果"""
    basic_features: Dict[str, Any]
    pattern_features: Dict[str, Any]
    trend_features: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {}
        result.update(self.basic_features)
        result.update(self.pattern_features)
        result.update(self.trend_features)
        return result


class LotteryFeatureCalculator:
    """福彩3D特征计算器"""
    
    def __init__(self):
        """初始化特征计算器"""
        # 质数集合 (0-9范围内) - 修正：1 2 3 5 7为质
        self.prime_numbers = {1, 2, 3, 5, 7}

        # 合数集合 (0-9范围内) - 修正：0 4 6 8 9为合
        self.composite_numbers = {0, 4, 6, 8, 9}

        # 大小数字分界线 - 修正：0-4为小，5-9为大
        self.big_small_threshold = 5

        # 大中小数字分界 - 修正：012为小，3456为中，789为大
        self.small_range = (0, 2)   # 小数: 0-2
        self.medium_range = (3, 6)  # 中数: 3-6
        self.big_range = (7, 9)     # 大数: 7-9

        # 012路映射 - 修正：0369为0路，147为1路，258为2路
        self.route_map = {
            0: 0, 3: 0, 6: 0, 9: 0,  # 0路
            1: 1, 4: 1, 7: 1,        # 1路
            2: 2, 5: 2, 8: 2         # 2路
        }
    
    def calculate_all_features(self, hundreds: int, tens: int, units: int, 
                             previous_hundreds: Optional[int] = None,
                             previous_tens: Optional[int] = None,
                             previous_units: Optional[int] = None) -> FeatureResult:
        """
        计算所有特征
        
        Args:
            hundreds: 百位数字
            tens: 十位数字
            units: 个位数字
            previous_hundreds: 上期百位数字（用于走势计算）
            previous_tens: 上期十位数字（用于走势计算）
            previous_units: 上期个位数字（用于走势计算）
            
        Returns:
            FeatureResult: 包含所有特征的结果对象
        """
        numbers = [hundreds, tens, units]
        
        # 计算基础特征
        basic_features = self._calculate_basic_features(numbers)
        
        # 计算模式特征
        pattern_features = self._calculate_pattern_features(numbers)
        
        # 计算走势特征
        trend_features = self._calculate_trend_features(
            numbers, 
            [previous_hundreds, previous_tens, previous_units] if previous_hundreds is not None else None
        )
        
        return FeatureResult(
            basic_features=basic_features,
            pattern_features=pattern_features,
            trend_features=trend_features
        )
    
    def _calculate_basic_features(self, numbers: List[int]) -> Dict[str, Any]:
        """计算基础特征"""
        hundreds, tens, units = numbers
        sum_value = sum(numbers)
        
        features = {
            # 和值相关
            'sum_value': sum_value,
            'sum_tail': sum_value % 10,  # 和尾
            
            # 跨度
            'span': max(numbers) - min(numbers),
            
            # 奇偶特征
            'odd_count': sum(num % 2 for num in numbers),
            'even_count': sum(1 - (num % 2) for num in numbers),
            'all_odd': all(num % 2 == 1 for num in numbers),
            'all_even': all(num % 2 == 0 for num in numbers),
            'odd_even_pattern': ''.join('奇' if num % 2 == 1 else '偶' for num in numbers),
            
            # 大小特征
            'big_count': sum(1 for num in numbers if num >= self.big_small_threshold),
            'small_count': sum(1 for num in numbers if num < self.big_small_threshold),
            'all_big': all(num >= self.big_small_threshold for num in numbers),
            'all_small': all(num < self.big_small_threshold for num in numbers),
            'big_small_pattern': ''.join('大' if num >= self.big_small_threshold else '小' for num in numbers),
            
            # 大中小特征
            'big_medium_small_count': self._get_big_medium_small_count(numbers),
            'big_medium_small_pattern': self._get_big_medium_small_pattern(numbers),
            
            # 质合特征 - 修正：1 2 3 5 7为质，0 4 6 8 9为合
            'prime_count': sum(1 for num in numbers if num in self.prime_numbers),
            'composite_count': sum(1 for num in numbers if num in self.composite_numbers),
            'prime_composite_pattern': self._get_prime_composite_pattern(numbers),

            # 012路特征 - 修正：0369为0路，147为1路，258为2路
            'route_012_count': self._get_route_012_count(numbers),
            'route_012_pattern': self._get_route_012_pattern(numbers),
        }
        
        return features
    
    def _calculate_pattern_features(self, numbers: List[int]) -> Dict[str, Any]:
        """计算模式特征"""
        hundreds, tens, units = numbers
        
        features = {
            # 连号特征
            'has_consecutive': self._has_consecutive_numbers(numbers),
            'consecutive_count': self._count_consecutive_pairs(numbers),
            
            # 重复数字特征
            'has_repeat': len(set(numbers)) < 3,
            'repeat_count': 3 - len(set(numbers)),
            'number_type': self._determine_number_type(numbers),
            
            # 形态特征
            'is_convex': self._is_convex_shape(numbers),      # 凸起形 (如 131, 242)
            'is_concave': self._is_concave_shape(numbers),    # 凹下形 (如 313, 424)
            'is_ascending': self._is_ascending_shape(numbers), # 上升形 (如 123, 456)
            'is_descending': self._is_descending_shape(numbers), # 下降形 (如 321, 654)
            'is_parallel': self._is_parallel_shape(numbers),   # 平行形 (如 111, 222)
            
            # 位置关系特征
            'hundreds_tens_diff': hundreds - tens,
            'tens_units_diff': tens - units,
            'hundreds_units_diff': hundreds - units,
            'max_position_diff': max(abs(hundreds - tens), abs(tens - units), abs(hundreds - units)),
            
            # 数字分布特征
            'unique_count': len(set(numbers)),
            'is_all_different': len(set(numbers)) == 3,
            'is_leopard': len(set(numbers)) == 1,  # 豹子
            'is_pair': len(set(numbers)) == 2,     # 对子
        }
        
        return features
    
    def _calculate_trend_features(self, current_numbers: List[int], 
                                previous_numbers: Optional[List[int]] = None) -> Dict[str, Any]:
        """计算走势特征"""
        features = {}
        
        if previous_numbers is None or None in previous_numbers:
            # 如果没有上期数据，返回默认值
            features.update({
                'hundreds_trend': '无',
                'tens_trend': '无',
                'units_trend': '无',
                'sum_trend': '无',
                'span_trend': '无',
                'overall_trend': '无'
            })
            return features
        
        current_hundreds, current_tens, current_units = current_numbers
        previous_hundreds, previous_tens, previous_units = previous_numbers
        
        # 各位走势
        features['hundreds_trend'] = self._get_position_trend(current_hundreds, previous_hundreds)
        features['tens_trend'] = self._get_position_trend(current_tens, previous_tens)
        features['units_trend'] = self._get_position_trend(current_units, previous_units)
        
        # 和值走势
        current_sum = sum(current_numbers)
        previous_sum = sum(previous_numbers)
        features['sum_trend'] = self._get_value_trend(current_sum, previous_sum)
        
        # 跨度走势
        current_span = max(current_numbers) - min(current_numbers)
        previous_span = max(previous_numbers) - min(previous_numbers)
        features['span_trend'] = self._get_value_trend(current_span, previous_span)
        
        # 整体走势评估
        trend_scores = [
            1 if features['hundreds_trend'] == '上升' else -1 if features['hundreds_trend'] == '下降' else 0,
            1 if features['tens_trend'] == '上升' else -1 if features['tens_trend'] == '下降' else 0,
            1 if features['units_trend'] == '上升' else -1 if features['units_trend'] == '下降' else 0
        ]
        
        total_trend_score = sum(trend_scores)
        if total_trend_score > 0:
            features['overall_trend'] = '上升'
        elif total_trend_score < 0:
            features['overall_trend'] = '下降'
        else:
            features['overall_trend'] = '平稳'
        
        return features
    
    def _get_big_medium_small_count(self, numbers: List[int]) -> Dict[str, int]:
        """获取大中小数字计数 - 修正：012为小，3456为中，789为大"""
        small_count = sum(1 for num in numbers if self.small_range[0] <= num <= self.small_range[1])
        medium_count = sum(1 for num in numbers if self.medium_range[0] <= num <= self.medium_range[1])
        big_count = sum(1 for num in numbers if self.big_range[0] <= num <= self.big_range[1])

        return {
            'small_count': small_count,
            'medium_count': medium_count,
            'big_count': big_count
        }
    
    def _get_big_medium_small_pattern(self, numbers: List[int]) -> str:
        """获取大中小模式字符串"""
        pattern = []
        for num in numbers:
            if self.small_range[0] <= num <= self.small_range[1]:
                pattern.append('小')
            elif self.medium_range[0] <= num <= self.medium_range[1]:
                pattern.append('中')
            else:  # self.big_range[0] <= num <= self.big_range[1]
                pattern.append('大')
        return ''.join(pattern)
    
    def _get_prime_composite_pattern(self, numbers: List[int]) -> str:
        """获取质合模式字符串 - 修正：1 2 3 5 7为质，0 4 6 8 9为合"""
        pattern = []
        for num in numbers:
            if num in self.prime_numbers:
                pattern.append('质')
            elif num in self.composite_numbers:
                pattern.append('合')
            else:
                # 理论上不应该到这里，因为0-9都已经分类了
                pattern.append('未知')
        return ''.join(pattern)
    
    def _get_route_012_count(self, numbers: List[int]) -> Dict[str, int]:
        """获取012路计数 - 修正：0369为0路，147为1路，258为2路"""
        route_counts = {0: 0, 1: 0, 2: 0}
        for num in numbers:
            route = self.route_map[num]
            route_counts[route] += 1

        return {
            'route_0_count': route_counts[0],
            'route_1_count': route_counts[1],
            'route_2_count': route_counts[2]
        }

    def _get_route_012_pattern(self, numbers: List[int]) -> str:
        """获取012路模式字符串 - 修正：0369为0路，147为1路，258为2路"""
        return ''.join(str(self.route_map[num]) for num in numbers)
    
    def _has_consecutive_numbers(self, numbers: List[int]) -> bool:
        """检查是否有连号"""
        sorted_numbers = sorted(numbers)
        for i in range(len(sorted_numbers) - 1):
            if sorted_numbers[i + 1] - sorted_numbers[i] == 1:
                return True
        return False
    
    def _count_consecutive_pairs(self, numbers: List[int]) -> int:
        """计算连号对数"""
        count = 0
        sorted_numbers = sorted(numbers)
        for i in range(len(sorted_numbers) - 1):
            if sorted_numbers[i + 1] - sorted_numbers[i] == 1:
                count += 1
        return count
    
    def _determine_number_type(self, numbers: List[int]) -> str:
        """判断号码类型"""
        unique_count = len(set(numbers))
        if unique_count == 1:
            return "豹子"
        elif unique_count == 2:
            return "对子"
        else:
            return "组六"
    
    def _is_convex_shape(self, numbers: List[int]) -> bool:
        """判断是否为凸起形（中间数字最大）"""
        hundreds, tens, units = numbers
        return tens > hundreds and tens > units
    
    def _is_concave_shape(self, numbers: List[int]) -> bool:
        """判断是否为凹下形（中间数字最小）"""
        hundreds, tens, units = numbers
        return tens < hundreds and tens < units
    
    def _is_ascending_shape(self, numbers: List[int]) -> bool:
        """判断是否为上升形"""
        hundreds, tens, units = numbers
        return hundreds <= tens <= units
    
    def _is_descending_shape(self, numbers: List[int]) -> bool:
        """判断是否为下降形"""
        hundreds, tens, units = numbers
        return hundreds >= tens >= units
    
    def _is_parallel_shape(self, numbers: List[int]) -> bool:
        """判断是否为平行形（所有数字相同）"""
        return len(set(numbers)) == 1
    
    def _get_position_trend(self, current: int, previous: int) -> str:
        """获取位置走势"""
        if current > previous:
            return '上升'
        elif current < previous:
            return '下降'
        else:
            return '平稳'
    
    def _get_value_trend(self, current: int, previous: int) -> str:
        """获取数值走势"""
        if current > previous:
            return '上升'
        elif current < previous:
            return '下降'
        else:
            return '平稳'


# 便捷函数
def calculate_features(hundreds: int, tens: int, units: int, 
                      previous_hundreds: Optional[int] = None,
                      previous_tens: Optional[int] = None,
                      previous_units: Optional[int] = None) -> Dict[str, Any]:
    """
    便捷的特征计算函数
    
    Args:
        hundreds: 百位数字
        tens: 十位数字
        units: 个位数字
        previous_hundreds: 上期百位数字
        previous_tens: 上期十位数字
        previous_units: 上期个位数字
        
    Returns:
        Dict[str, Any]: 包含所有特征的字典
    """
    calculator = LotteryFeatureCalculator()
    result = calculator.calculate_all_features(
        hundreds, tens, units,
        previous_hundreds, previous_tens, previous_units
    )
    return result.to_dict()
