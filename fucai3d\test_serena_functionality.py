#!/usr/bin/env python3
"""
Serena功能测试脚本
验证Serena MCP服务器的基本功能是否正常工作
"""

import subprocess
import sys
import json
import time
import os
from pathlib import Path

def test_serena_executable():
    """测试serena可执行文件"""
    print("🔍 测试Serena可执行文件...")
    
    try:
        # 测试serena命令
        result = subprocess.run(
            ["serena", "--help"], 
            capture_output=True, 
            text=True,
            timeout=30
        )
        if result.returncode == 0:
            print("✅ serena命令可用")
            return True
        else:
            print(f"❌ serena命令失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_serena_mcp_server_exe():
    """测试serena-mcp-server.exe"""
    print("\n🔍 测试serena-mcp-server.exe...")
    
    exe_path = Path("venv/Scripts/serena-mcp-server.exe")
    if not exe_path.exists():
        print(f"❌ 文件不存在: {exe_path}")
        return False
    
    print(f"✅ 文件存在: {exe_path}")
    
    try:
        # 测试help命令
        result = subprocess.run(
            [str(exe_path), "--help"], 
            capture_output=True, 
            text=True,
            timeout=30
        )
        if result.returncode == 0:
            print("✅ serena-mcp-server.exe可执行")
            return True
        else:
            print(f"❌ serena-mcp-server.exe执行失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_mcp_server_parameters():
    """测试MCP服务器参数"""
    print("\n🔍 测试MCP服务器参数...")
    
    exe_path = Path("venv/Scripts/serena-mcp-server.exe")
    
    try:
        # 测试带参数的help命令
        result = subprocess.run(
            [
                str(exe_path), 
                "--context", "ide-assistant",
                "--project", "d:/github/fucai3d",
                "--help"
            ], 
            capture_output=True, 
            text=True,
            timeout=30
        )
        if result.returncode == 0:
            print("✅ MCP服务器参数正确")
            return True
        else:
            print(f"❌ MCP服务器参数测试失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_project_tools():
    """测试项目工具"""
    print("\n🔍 测试项目工具...")
    
    try:
        # 测试项目工具列表
        result = subprocess.run(
            ["serena", "tools", "list"], 
            capture_output=True, 
            text=True,
            timeout=30
        )
        if result.returncode == 0:
            print("✅ 项目工具列表可用")
            tools_count = len([line for line in result.stdout.split('\n') if line.strip().startswith('*')])
            print(f"✅ 发现 {tools_count} 个工具")
            return True
        else:
            print(f"❌ 项目工具测试失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_config_generation():
    """测试配置生成"""
    print("\n🔍 测试配置生成...")
    
    try:
        # 测试配置编辑命令
        result = subprocess.run(
            ["serena", "config", "--help"], 
            capture_output=True, 
            text=True,
            timeout=30
        )
        if result.returncode == 0:
            print("✅ 配置命令可用")
            return True
        else:
            print(f"❌ 配置命令测试失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def show_mcp_config():
    """显示MCP配置"""
    print("\n📋 推荐的MCP配置:")
    
    config = {
        "mcpServers": {
            "serena": {
                "command": "d:/github/fucai3d/venv/Scripts/serena-mcp-server.exe",
                "args": [
                    "--context",
                    "ide-assistant",
                    "--project",
                    "d:/github/fucai3d"
                ]
            }
        }
    }
    
    print(json.dumps(config, indent=2, ensure_ascii=False))
    
    print("\n📍 配置文件位置:")
    print("- Claude Desktop: %APPDATA%\\Claude\\claude_desktop_config.json")
    print("- Augment: 查看Augment配置目录")
    print("- 其他MCP客户端: 查看相应文档")

def show_next_steps():
    """显示下一步操作"""
    print("\n🚀 下一步操作:")
    print("1. 将上述MCP配置添加到您的MCP客户端")
    print("2. 重启MCP客户端")
    print("3. 验证Serena工具是否在客户端中可用")
    print("4. 测试基本功能:")
    print("   - initial_instructions_serena()")
    print("   - get_active_project()")
    print("   - activate_project('d:/github/fucai3d')")
    print("\n🌐 预期Web仪表板: http://127.0.0.1:24282/dashboard/index.html")

def main():
    """主函数"""
    print("🚀 Serena功能测试")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists("venv"):
        print("❌ 虚拟环境不存在，请确保在fucai3d项目目录中运行")
        return False
    
    print("✅ 在正确的项目目录中")
    
    # 运行所有测试
    tests = [
        test_serena_executable,
        test_serena_mcp_server_exe,
        test_mcp_server_parameters,
        test_project_tools,
        test_config_generation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Serena已准备就绪")
        show_mcp_config()
        show_next_steps()
        return True
    else:
        print("⚠️ 部分测试失败，请检查安装")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
