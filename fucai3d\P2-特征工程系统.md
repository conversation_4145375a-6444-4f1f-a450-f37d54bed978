# P2-特征工程系统

## 项目概述
**前置条件**：P1-数据采集与存储基础完成
**核心目标**：为不同预测器构建专用特征体系
**预计时间**：1周
**更新状态**：✅ 已集成实时特征计算系统（2025-01-14）

## 技术架构决策

### 🎯 特征存储策略（重要更新）

**推荐方案：智能混合策略**

基于性能分析和实际需求，采用以下策略：

1. **主策略：实时计算** ⭐
   - 基础特征实时计算（<1毫秒）
   - 保持数据一致性和系统简洁性
   - 适用于日常查询和用户交互

2. **辅助策略：选择性缓存**
   - 热点数据LRU缓存
   - 复杂统计特征缓存
   - 内存使用可控

3. **专用策略：ML训练优化**
   - 训练时临时批量预计算
   - 训练完成后自动清理
   - 避免永久存储开销

### 特征分类体系
- **百位特征**：专门用于百位数字预测
- **十位特征**：专门用于十位数字预测
- **个位特征**：专门用于个位数字预测
- **和值特征**：专门用于和值预测
- **跨度特征**：专门用于跨度预测
- **通用特征**：所有预测器共享的基础特征

### 数据库设计（优化版）
```sql
-- 特征缓存表（可选，用于性能优化）
CREATE TABLE feature_cache (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    issue TEXT NOT NULL,
    feature_type TEXT NOT NULL,          -- hundreds/tens/units/sum/span/common
    feature_vector TEXT NOT NULL,        -- JSON格式的特征向量
    feature_names TEXT NOT NULL,         -- JSON格式的特征名称
    cache_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    access_count INTEGER DEFAULT 1,      -- 访问次数，用于LRU
    FOREIGN KEY (issue) REFERENCES lottery_data(issue),
    UNIQUE(issue, feature_type)
);

-- ML训练临时表（训练时创建，完成后删除）
CREATE TABLE temp_ml_features (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    issue TEXT NOT NULL,
    feature_type TEXT NOT NULL,
    feature_vector TEXT NOT NULL,
    feature_names TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (issue) REFERENCES lottery_data(issue)
);

-- 特征重要性表（保持不变）
CREATE TABLE feature_importance (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    feature_type TEXT NOT NULL,
    feature_name TEXT NOT NULL,
    importance_score REAL NOT NULL,
    model_type TEXT NOT NULL,            -- xgb/lgb/lstm
    calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 核心功能实现

### 1. 智能特征工程类（集成实时计算+缓存优化）
```python
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import json
import sqlite3
from datetime import datetime, timedelta
from functools import lru_cache
import threading

# 导入新的特征服务
from src.data.feature_service import FeatureService
from src.data.feature_calculator import LotteryFeatureCalculator

class IntelligentFeatureEngineer:
    def __init__(self, db_path: str, cache_size: int = 1000, enable_cache: bool = True):
        self.db_path = db_path
        self.enable_cache = enable_cache
        self.cache_size = cache_size
        self.feature_config = {
            'window_sizes': [5, 10, 20, 30, 50, 100],  # 不同的时间窗口
            'lag_periods': [1, 2, 3, 5, 7, 10],       # 滞后期数
            'statistical_periods': [10, 30, 100]       # 统计周期
        }

        # 初始化新的特征服务
        self.feature_service = FeatureService(db_path)
        self.calculator = LotteryFeatureCalculator()

        # 内存缓存
        self._cache = {}
        self._cache_lock = threading.Lock()

        # 初始化缓存表（如果启用）
        if self.enable_cache:
            self._init_cache_table()

    def _init_cache_table(self):
        """初始化缓存表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS feature_cache (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                issue TEXT NOT NULL,
                feature_type TEXT NOT NULL,
                feature_vector TEXT NOT NULL,
                feature_names TEXT NOT NULL,
                cache_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                access_count INTEGER DEFAULT 1,
                FOREIGN KEY (issue) REFERENCES lottery_data(issue),
                UNIQUE(issue, feature_type)
            )
        """)
        conn.commit()
        conn.close()

    def load_data(self) -> pd.DataFrame:
        """加载数据"""
        conn = sqlite3.connect(self.db_path)
        df = pd.read_sql_query("""
            SELECT issue, draw_date, hundreds, tens, units, sum_value, span, number_type
            FROM lottery_data
            ORDER BY issue
        """, conn)
        conn.close()

        df['draw_date'] = pd.to_datetime(df['draw_date'])
        return df

    def get_features_with_cache(self, issue: str, feature_type: str) -> Optional[Dict]:
        """带缓存的特征获取"""
        cache_key = f"{issue}_{feature_type}"

        # 检查内存缓存
        with self._cache_lock:
            if cache_key in self._cache:
                return self._cache[cache_key]

        # 检查数据库缓存
        if self.enable_cache:
            cached_features = self._get_from_db_cache(issue, feature_type)
            if cached_features:
                # 更新内存缓存
                with self._cache_lock:
                    self._cache[cache_key] = cached_features
                    # LRU清理
                    if len(self._cache) > self.cache_size:
                        oldest_key = next(iter(self._cache))
                        del self._cache[oldest_key]
                return cached_features

        # 实时计算
        features = self._calculate_features_real_time(issue, feature_type)

        # 缓存结果
        if features and self.enable_cache:
            self._save_to_cache(issue, feature_type, features)
            with self._cache_lock:
                self._cache[cache_key] = features

        return features

    def _get_from_db_cache(self, issue: str, feature_type: str) -> Optional[Dict]:
        """从数据库缓存获取特征"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("""
            SELECT feature_vector, feature_names FROM feature_cache
            WHERE issue = ? AND feature_type = ?
        """, (issue, feature_type))

        row = cursor.fetchone()
        if row:
            # 更新访问次数
            cursor.execute("""
                UPDATE feature_cache SET access_count = access_count + 1
                WHERE issue = ? AND feature_type = ?
            """, (issue, feature_type))
            conn.commit()

            features = {
                'vector': json.loads(row[0]),
                'names': json.loads(row[1])
            }
            conn.close()
            return features

        conn.close()
        return None

    def _save_to_cache(self, issue: str, feature_type: str, features: Dict):
        """保存到缓存"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("""
            INSERT OR REPLACE INTO feature_cache
            (issue, feature_type, feature_vector, feature_names)
            VALUES (?, ?, ?, ?)
        """, (issue, feature_type,
              json.dumps(features['vector']),
              json.dumps(features['names'])))
        conn.commit()
        conn.close()

    def _calculate_features_real_time(self, issue: str, feature_type: str) -> Optional[Dict]:
        """实时计算特征"""
        # 获取基础特征
        base_features = self.feature_service.get_features_for_issue(issue, include_trend=True)
        if not base_features:
            return None

        # 根据特征类型生成高级特征
        df = self.load_data()
        issue_data = df[df['issue'] == issue]
        if issue_data.empty:
            return None

        if feature_type == 'hundreds':
            features_df = self.generate_hundreds_features(df, None)
        elif feature_type == 'tens':
            features_df = self.generate_tens_features(df, None)
        elif feature_type == 'units':
            features_df = self.generate_units_features(df, None)
        elif feature_type == 'sum':
            features_df = self.generate_sum_features(df, None)
        elif feature_type == 'span':
            features_df = self.generate_span_features(df, None)
        elif feature_type == 'common':
            features_df = self.generate_common_features(df, None)
        else:
            return None

        # 获取指定期号的特征
        issue_features = features_df[features_df['issue'] == issue]
        if issue_features.empty:
            return None

        feature_vector = issue_features.drop('issue', axis=1).values.tolist()[0]
        feature_names = issue_features.drop('issue', axis=1).columns.tolist()

        return {
            'vector': feature_vector,
            'names': feature_names
        }

    def get_instant_features(self, issue: str) -> Dict[str, Any]:
        """获取指定期号的即时特征（优化版）"""
        return self.feature_service.get_features_for_issue(issue, include_trend=True)

    def calculate_features_for_numbers(self, hundreds: int, tens: int, units: int) -> Dict[str, Any]:
        """为给定号码计算特征（实时计算）"""
        return self.feature_service.calculate_feature_for_numbers(hundreds, tens, units)

    def prepare_ml_training_data(self, feature_types: List[str], start_issue: str = None, end_issue: str = None) -> pd.DataFrame:
        """为机器学习训练准备数据（批量优化）"""
        print("开始准备ML训练数据...")

        # 创建临时表
        self._create_temp_ml_table()

        try:
            df = self.load_data()
            if start_issue:
                df = df[df['issue'] >= start_issue]
            if end_issue:
                df = df[df['issue'] <= end_issue]

            # 批量计算所有特征
            all_features = {}
            for feature_type in feature_types:
                print(f"计算{feature_type}特征...")
                if feature_type == 'hundreds':
                    features_df = self.generate_hundreds_features(df, None)
                elif feature_type == 'tens':
                    features_df = self.generate_tens_features(df, None)
                elif feature_type == 'units':
                    features_df = self.generate_units_features(df, None)
                elif feature_type == 'sum':
                    features_df = self.generate_sum_features(df, None)
                elif feature_type == 'span':
                    features_df = self.generate_span_features(df, None)
                elif feature_type == 'common':
                    features_df = self.generate_common_features(df, None)

                all_features[feature_type] = features_df

            # 合并所有特征
            combined_df = df[['issue', 'draw_date', 'hundreds', 'tens', 'units']].copy()
            for feature_type, features_df in all_features.items():
                combined_df = combined_df.merge(features_df, on='issue', how='left')

            print("ML训练数据准备完成")
            return combined_df

        finally:
            # 清理临时表
            self._cleanup_temp_ml_table()

    def _create_temp_ml_table(self):
        """创建临时ML表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS temp_ml_features (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                issue TEXT NOT NULL,
                feature_type TEXT NOT NULL,
                feature_vector TEXT NOT NULL,
                feature_names TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (issue) REFERENCES lottery_data(issue)
            )
        """)
        conn.commit()
        conn.close()

    def _cleanup_temp_ml_table(self):
        """清理临时ML表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("DROP TABLE IF EXISTS temp_ml_features")
        conn.commit()
        conn.close()

    def clear_cache(self):
        """清理缓存"""
        with self._cache_lock:
            self._cache.clear()

        if self.enable_cache:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("DELETE FROM feature_cache")
            conn.commit()
            conn.close()

        print("缓存已清理")
```

### 2. 百位特征工程（已优化集成基础特征）
```python
def generate_hundreds_features(self, df: pd.DataFrame, base_features_df: pd.DataFrame) -> pd.DataFrame:
    """生成百位专用特征（集成新特征计算器的基础特征）"""
    features = pd.DataFrame()
    features['issue'] = df['issue']

    # 1. 基础特征（从新特征计算器获取）
    features['hundreds_current'] = df['hundreds']

    # 集成新计算器的基础特征
    if not base_features_df.empty:
        # 添加奇偶、大小、质合等基础特征
        features['hundreds_is_odd'] = base_features_df['hundreds'].apply(lambda x: x % 2)
        features['hundreds_is_big'] = base_features_df['hundreds'].apply(lambda x: 1 if x >= 5 else 0)
        features['hundreds_is_prime'] = base_features_df['hundreds'].apply(lambda x: 1 if x in {2, 3, 5, 7} else 0)
        features['hundreds_route'] = base_features_df['hundreds'].apply(lambda x: x % 3)
    
    # 2. 历史频次特征
    for window in self.feature_config['window_sizes']:
        for digit in range(10):
            col_name = f'hundreds_freq_{digit}_last_{window}'
            features[col_name] = df['hundreds'].rolling(window=window).apply(
                lambda x: (x == digit).sum() / len(x) if len(x) > 0 else 0
            )
    
    # 3. 遗漏特征
    for digit in range(10):
        col_name = f'hundreds_missing_{digit}'
        missing_periods = []
        last_appear = -1
        
        for i, val in enumerate(df['hundreds']):
            if val == digit:
                last_appear = i
            missing_periods.append(i - last_appear if last_appear >= 0 else i + 1)
        
        features[col_name] = missing_periods
    
    # 4. 冷热度特征
    for window in [10, 30]:
        col_name = f'hundreds_hot_cold_{window}'
        features[col_name] = df['hundreds'].rolling(window=window).apply(
            lambda x: len(set(x)) / len(x) if len(x) > 0 else 0  # 数字分散度
        )
    
    # 5. 趋势特征
    for window in [5, 10, 20]:
        col_name = f'hundreds_trend_{window}'
        features[col_name] = df['hundreds'].rolling(window=window).apply(
            lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) > 1 else 0  # 线性趋势
        )
    
    # 6. 滞后特征
    for lag in self.feature_config['lag_periods']:
        features[f'hundreds_lag_{lag}'] = df['hundreds'].shift(lag)
    
    # 7. 统计特征
    for window in self.feature_config['statistical_periods']:
        features[f'hundreds_mean_{window}'] = df['hundreds'].rolling(window=window).mean()
        features[f'hundreds_std_{window}'] = df['hundreds'].rolling(window=window).std()
        features[f'hundreds_min_{window}'] = df['hundreds'].rolling(window=window).min()
        features[f'hundreds_max_{window}'] = df['hundreds'].rolling(window=window).max()
    
    # 8. 周期性特征
    features['hundreds_weekday'] = df['draw_date'].dt.dayofweek
    features['hundreds_month'] = df['draw_date'].dt.month
    features['hundreds_quarter'] = df['draw_date'].dt.quarter
    
    # 9. 组合特征
    features['hundreds_is_even'] = (df['hundreds'] % 2 == 0).astype(int)
    features['hundreds_is_prime'] = df['hundreds'].apply(lambda x: x in [2, 3, 5, 7]).astype(int)
    features['hundreds_digit_sum'] = df['hundreds']  # 单位数字，数字和就是自己
    
    return features.fillna(0)
```

### 3. 十位特征工程
```python
def generate_tens_features(self, df: pd.DataFrame) -> pd.DataFrame:
    """生成十位专用特征"""
    features = pd.DataFrame()
    features['issue'] = df['issue']
    
    # 与百位特征类似的结构，但针对十位数字
    features['tens_current'] = df['tens']
    
    # 历史频次特征
    for window in self.feature_config['window_sizes']:
        for digit in range(10):
            col_name = f'tens_freq_{digit}_last_{window}'
            features[col_name] = df['tens'].rolling(window=window).apply(
                lambda x: (x == digit).sum() / len(x) if len(x) > 0 else 0
            )
    
    # 遗漏特征
    for digit in range(10):
        col_name = f'tens_missing_{digit}'
        missing_periods = []
        last_appear = -1
        
        for i, val in enumerate(df['tens']):
            if val == digit:
                last_appear = i
            missing_periods.append(i - last_appear if last_appear >= 0 else i + 1)
        
        features[col_name] = missing_periods
    
    # 与百位的关联特征
    features['tens_hundreds_diff'] = df['tens'] - df['hundreds']
    features['tens_hundreds_same'] = (df['tens'] == df['hundreds']).astype(int)
    
    # 其他特征类似百位特征的实现...
    
    return features.fillna(0)
```

### 4. 个位特征工程
```python
def generate_units_features(self, df: pd.DataFrame) -> pd.DataFrame:
    """生成个位专用特征"""
    features = pd.DataFrame()
    features['issue'] = df['issue']
    
    features['units_current'] = df['units']
    
    # 个位特有的特征
    features['units_hundreds_diff'] = df['units'] - df['hundreds']
    features['units_tens_diff'] = df['units'] - df['tens']
    features['units_is_ascending'] = ((df['hundreds'] < df['tens']) & (df['tens'] < df['units'])).astype(int)
    features['units_is_descending'] = ((df['hundreds'] > df['tens']) & (df['tens'] > df['units'])).astype(int)
    
    # 其他特征类似百位特征的实现...
    
    return features.fillna(0)
```

### 5. 和值特征工程
```python
def generate_sum_features(self, df: pd.DataFrame) -> pd.DataFrame:
    """生成和值专用特征"""
    features = pd.DataFrame()
    features['issue'] = df['issue']
    
    features['sum_current'] = df['sum_value']
    
    # 和值分布特征
    for window in self.feature_config['window_sizes']:
        features[f'sum_mean_{window}'] = df['sum_value'].rolling(window=window).mean()
        features[f'sum_std_{window}'] = df['sum_value'].rolling(window=window).std()
        features[f'sum_median_{window}'] = df['sum_value'].rolling(window=window).median()
        features[f'sum_q25_{window}'] = df['sum_value'].rolling(window=window).quantile(0.25)
        features[f'sum_q75_{window}'] = df['sum_value'].rolling(window=window).quantile(0.75)
    
    # 和值范围特征
    features['sum_is_small'] = (df['sum_value'] <= 9).astype(int)    # 小和值
    features['sum_is_medium'] = ((df['sum_value'] >= 10) & (df['sum_value'] <= 17)).astype(int)  # 中和值
    features['sum_is_large'] = (df['sum_value'] >= 18).astype(int)   # 大和值
    
    # 和值尾数特征
    features['sum_tail'] = df['sum_value'] % 10
    for tail in range(10):
        col_name = f'sum_tail_{tail}_freq_30'
        features[col_name] = (df['sum_value'] % 10).rolling(window=30).apply(
            lambda x: (x == tail).sum() / len(x) if len(x) > 0 else 0
        )
    
    # 和值变化特征
    features['sum_diff_1'] = df['sum_value'].diff(1)
    features['sum_diff_2'] = df['sum_value'].diff(2)
    features['sum_change_direction'] = np.sign(features['sum_diff_1'])
    
    # 和值周期特征
    for period in [7, 14, 30]:
        features[f'sum_cycle_mean_{period}'] = df['sum_value'].rolling(window=period).mean()
        features[f'sum_vs_cycle_{period}'] = df['sum_value'] - features[f'sum_cycle_mean_{period}']
    
    return features.fillna(0)
```

### 6. 跨度特征工程
```python
def generate_span_features(self, df: pd.DataFrame) -> pd.DataFrame:
    """生成跨度专用特征"""
    features = pd.DataFrame()
    features['issue'] = df['issue']
    
    features['span_current'] = df['span']
    
    # 跨度分布特征
    for span_val in range(10):
        col_name = f'span_{span_val}_freq_30'
        features[col_name] = df['span'].rolling(window=30).apply(
            lambda x: (x == span_val).sum() / len(x) if len(x) > 0 else 0
        )
    
    # 跨度统计特征
    for window in self.feature_config['window_sizes']:
        features[f'span_mean_{window}'] = df['span'].rolling(window=window).mean()
        features[f'span_mode_{window}'] = df['span'].rolling(window=window).apply(
            lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else 0
        )
    
    # 跨度变化特征
    features['span_diff_1'] = df['span'].diff(1)
    features['span_stability'] = df['span'].rolling(window=10).std()
    
    # 跨度与和值关系
    features['span_sum_ratio'] = df['span'] / (df['sum_value'] + 1)  # 避免除零
    features['span_sum_correlation'] = df['span'].rolling(window=30).corr(df['sum_value'])
    
    return features.fillna(0)
```

### 7. 通用特征工程
```python
def generate_common_features(self, df: pd.DataFrame) -> pd.DataFrame:
    """生成通用特征"""
    features = pd.DataFrame()
    features['issue'] = df['issue']
    
    # 号码类型特征
    features['is_leopard'] = (df['number_type'] == '豹子').astype(int)
    features['is_group3'] = (df['number_type'] == '组三').astype(int)
    features['is_group6'] = (df['number_type'] == '组六').astype(int)
    
    # 奇偶特征
    features['odd_count'] = ((df['hundreds'] % 2) + (df['tens'] % 2) + (df['units'] % 2))
    features['even_count'] = 3 - features['odd_count']
    features['all_odd'] = (features['odd_count'] == 3).astype(int)
    features['all_even'] = (features['even_count'] == 3).astype(int)
    
    # 大小特征
    features['big_count'] = ((df['hundreds'] >= 5) + (df['tens'] >= 5) + (df['units'] >= 5)).astype(int)
    features['small_count'] = 3 - features['big_count']
    
    # 质合特征
    prime_numbers = {2, 3, 5, 7}
    features['prime_count'] = (df['hundreds'].isin(prime_numbers) + 
                              df['tens'].isin(prime_numbers) + 
                              df['units'].isin(prime_numbers)).astype(int)
    
    # 012路特征
    features['route0_count'] = ((df['hundreds'] % 3 == 0) + 
                               (df['tens'] % 3 == 0) + 
                               (df['units'] % 3 == 0)).astype(int)
    features['route1_count'] = ((df['hundreds'] % 3 == 1) + 
                               (df['tens'] % 3 == 1) + 
                               (df['units'] % 3 == 1)).astype(int)
    features['route2_count'] = ((df['hundreds'] % 3 == 2) + 
                               (df['tens'] % 3 == 2) + 
                               (df['units'] % 3 == 2)).astype(int)
    
    # 连号特征
    features['has_consecutive'] = (
        (abs(df['hundreds'] - df['tens']) == 1) | 
        (abs(df['tens'] - df['units']) == 1) | 
        (abs(df['hundreds'] - df['units']) == 1)
    ).astype(int)
    
    # 重复数字特征
    features['has_repeat'] = (
        (df['hundreds'] == df['tens']) | 
        (df['tens'] == df['units']) | 
        (df['hundreds'] == df['units'])
    ).astype(int)
    
    # 时间特征
    features['day_of_week'] = df['draw_date'].dt.dayofweek
    features['month'] = df['draw_date'].dt.month
    features['quarter'] = df['draw_date'].dt.quarter
    features['is_weekend'] = (df['draw_date'].dt.dayofweek >= 5).astype(int)
    
    return features.fillna(0)
```

### 8. 特征重要性分析
```python
def analyze_feature_importance(self, feature_type: str, model_type: str = 'xgb'):
    """分析特征重要性"""
    from sklearn.ensemble import RandomForestClassifier
    import xgboost as xgb
    
    # 加载特征数据
    conn = sqlite3.connect(self.db_path)
    
    # 获取特征数据
    feature_query = """
        SELECT feature_vector, feature_names 
        FROM feature_data 
        WHERE feature_type = ?
        ORDER BY issue
    """
    
    cursor = conn.cursor()
    cursor.execute(feature_query, (feature_type,))
    rows = cursor.fetchall()
    
    if not rows:
        print(f"没有找到{feature_type}类型的特征数据")
        return
    
    # 构建特征矩阵
    X = []
    feature_names = json.loads(rows[0][1])
    
    for row in rows[:-1]:  # 除了最后一期
        X.append(json.loads(row[0]))
    
    # 获取目标变量
    if feature_type in ['hundreds', 'tens', 'units']:
        target_query = f"""
            SELECT {feature_type} 
            FROM lottery_data 
            ORDER BY issue 
            LIMIT -1 OFFSET 1
        """
    else:
        target_query = f"""
            SELECT {feature_type.replace('sum', 'sum_value')} 
            FROM lottery_data 
            ORDER BY issue 
            LIMIT -1 OFFSET 1
        """
    
    y = pd.read_sql_query(target_query, conn).iloc[:, 0].values
    
    conn.close()
    
    # 训练模型并计算特征重要性
    if model_type == 'xgb':
        model = xgb.XGBClassifier(n_estimators=100, random_state=42)
    else:
        model = RandomForestClassifier(n_estimators=100, random_state=42)
    
    model.fit(X, y)
    importance_scores = model.feature_importances_
    
    # 保存特征重要性
    self.save_feature_importance(feature_type, feature_names, importance_scores, model_type)
    
    return dict(zip(feature_names, importance_scores))

def save_feature_importance(self, feature_type: str, feature_names: List[str], 
                          importance_scores: np.ndarray, model_type: str):
    """保存特征重要性"""
    conn = sqlite3.connect(self.db_path)
    cursor = conn.cursor()
    
    for name, score in zip(feature_names, importance_scores):
        cursor.execute("""
            INSERT OR REPLACE INTO feature_importance 
            (feature_type, feature_name, importance_score, model_type)
            VALUES (?, ?, ?, ?)
        """, (feature_type, name, float(score), model_type))
    
    conn.commit()
    conn.close()
```

## 成功标准（更新版）

### 特征完整性
- [x] 基础特征：30+种实时计算特征（已完成）
- [ ] 百位特征：50+维高级特征
- [ ] 十位特征：50+维高级特征
- [ ] 个位特征：50+维高级特征
- [ ] 和值特征：30+维高级特征
- [ ] 跨度特征：20+维高级特征
- [ ] 通用特征：30+维高级特征

### 特征质量
- [x] 基础特征计算准确性100%（已验证）
- [ ] 高级特征无缺失值
- [ ] 特征数值范围合理
- [ ] 特征重要性分析完成

### 系统性能（实时计算优化版）
- [x] 单次特征计算 < 1毫秒（已达成）
- [x] 1000期批量计算 < 10毫秒（已达成）
- [x] 全量历史数据处理 < 5秒（已达成）
- [ ] 缓存命中率 > 80%
- [ ] ML训练数据准备 < 30秒

### 缓存性能
- [ ] 内存缓存响应时间 < 0.1毫秒
- [ ] 数据库缓存响应时间 < 1毫秒
- [ ] 缓存空间使用 < 50MB

## 部署说明（智能混合策略版）

### 基础使用（实时计算）
```python
# 实时特征计算
from src.data.feature_service import FeatureService

service = FeatureService("data/lottery.db")

# 获取指定期号特征
features = service.get_features_for_issue("2025001", include_trend=True)

# 计算给定号码特征
features = service.calculate_feature_for_numbers(1, 2, 3)

# 批量获取特征
batch_features = service.get_batch_features(["2025001", "2025002"])
```

### 高级特征工程（缓存优化）
```python
# 智能特征工程
engineer = IntelligentFeatureEngineer("data/lottery.db", cache_size=1000, enable_cache=True)

# 带缓存的特征获取
features = engineer.get_features_with_cache("2025001", "hundreds")

# 实时特征计算
instant_features = engineer.get_instant_features("2025001")

# 清理缓存
engineer.clear_cache()
```

### 机器学习训练优化
```python
# ML训练数据准备
engineer = IntelligentFeatureEngineer("data/lottery.db")

# 准备训练数据（自动批量计算+临时存储）
training_data = engineer.prepare_ml_training_data(
    feature_types=['hundreds', 'tens', 'units', 'sum', 'span'],
    start_issue="2020001",
    end_issue="2024365"
)

# 特征重要性分析
for feature_type in ['hundreds', 'tens', 'units', 'sum', 'span']:
    importance = engineer.analyze_feature_importance(feature_type)
    print(f"{feature_type}特征重要性: {importance}")

# 训练完成后自动清理临时数据
```

### 性能监控
```python
# 性能测试
import time

# 测试实时计算性能
start_time = time.time()
for i in range(1000):
    features = service.get_features_for_issue("2025001")
calc_time = time.time() - start_time
print(f"1000次查询耗时: {calc_time:.4f}秒")

# 测试缓存性能
start_time = time.time()
for i in range(1000):
    features = engineer.get_features_with_cache("2025001", "hundreds")
cache_time = time.time() - start_time
print(f"1000次缓存查询耗时: {cache_time:.4f}秒")
```

## 实施路线图

### 阶段1：基础优化（立即实施）
1. ✅ 集成实时特征计算系统
2. ✅ 验证计算准确性和性能
3. [ ] 添加内存缓存层
4. [ ] 实现LRU缓存策略

### 阶段2：高级特征（1周内）
1. [ ] 完善百位/十位/个位高级特征
2. [ ] 实现和值/跨度专用特征
3. [ ] 添加通用组合特征
4. [ ] 特征重要性分析

### 阶段3：ML优化（2周内）
1. [ ] ML训练数据预处理器
2. [ ] 批量计算优化
3. [ ] 临时存储管理
4. [ ] 性能监控和调优

### 阶段4：生产优化（1个月内）
1. [ ] 数据库缓存优化
2. [ ] 并发访问优化
3. [ ] 监控和告警系统
4. [ ] 自动化测试覆盖

## 技术债务和改进建议

### 当前限制
1. **内存使用** - 大批量计算时内存占用较高
2. **并发性能** - 高并发场景下需要优化
3. **缓存策略** - 需要更智能的缓存失效机制

### 未来改进方向
1. **分布式计算** - 考虑Redis集群缓存
2. **异步处理** - 非关键路径异步计算
3. **特征版本管理** - 支持特征定义版本控制
4. **自动化特征选择** - 基于ML的特征筛选

## 下一步
完成P2优化后，进入**P3-百位预测器**开发阶段，将基于优化后的特征工程系统构建高性能预测模型。
